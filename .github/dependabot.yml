version: 2

registries:
  bannerflow-github-nuget:
    type: nuget-feed
    url: https://nuget.pkg.github.com/nordicfactory/index.json
    username: nordicfactory
    password: ${{secrets.GH_PACKAGE_TOKEN}}

updates:
  - package-ecosystem: "github-actions"
    directory: "."
    schedule:
      interval: "weekly"
    groups:
      all-actions:
        dependency-type: "production"
        patterns:
          - "*"

  - package-ecosystem: "nuget"
    directory: "Studio"
    schedule:
      interval: "weekly"
    registries: "*"
    ignore:
      - dependency-name: "GraphQL*"
        versions: ["*"]
      - dependency-name: "FluentAssertions*"
        versions: [">= 8.0.0"]
      - dependency-name: "Swashbuckle*"
        versions: [">= 7.1.0"]
      - dependency-name: "MediatR"
        update-types: [ "version-update:semver-major" ]
      - dependency-name: "AutoMapper"
        update-types: [ "version-update:semver-major" ]
    groups:
      all-dependencies:
        dependency-type: "production"
        patterns:
          - "*"
      
