name: Build and Deploy

concurrency:
  group: ${{ github.head_ref || github.ref_name }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

on:
  push:
    branches:
      - main
    paths-ignore:
      - "*.md"
  workflow_dispatch:
  pull_request:
    types: [opened, synchronize]
    paths-ignore:
      - "*.md"

env:
  CONTAINER_NAME: "studio-api"
  NAMESPACE: "studio"
  NAME: "studioapi"
  BASE_DIRECTORY: "Studio"
  SONAR_PROJECT_KEY: "studio-api"
  FEATURE: "studio"
  BUILD_CHANNEL_ID: "studio-api-pull-requests"
  APP_RESOURCE_GROUP_PREFIX: "bf-Studio"
  PR_ENVIRONMENTS: "true"
  MESSAGE_PREFIX: ""

  # these are setup so they appear to be defined, they are set per environment later on
  BUILD_DESCRIPTION: ""
  DEPLOYMENT_DESCRIPTION: ""
  COMMIT_MESSAGE: ""
  secret-newrelic-api-key: ""
  warmup-endpoint: ""
  AZURE_DEV_SUBSCRIPTION_ID: "b736d7b3-0d45-4c37-9174-e671b53a6d83"
  auth0-keyvault-name: ""
  storage-account: ""
  storage-account-resource-group: ""
  servicebus: ""
  servicebus-resource-group: ""
  warmup-url: ""
  warmup-path: ""
  ENV_OVERRIDES: |
    {
      'sandbox': {
        'auth0-keyvault-name': 'bf-auth0-sandbox-kv',
        'storage-account': 'bannerflowsandbox',
        'storage-account-resource-group': 'bf-shared-sandbox-rg',
        'servicebus': 'bf-common-sandbox-asb',
        'servicebus-resource-group': 'bf-common-sandbox-rg',
        'warmup-url': 'https://sandbox-api.bannerflow.com',
        'warmup-path': '/studio/'
      },
      'production': {
        'auth0-keyvault-name': 'bf-auth0-kv',
        'storage-account': 'bannerflow',
        'storage-account-resource-group': 'bf-shared-rg',
        'servicebus': 'bf-common-asb',
        'servicebus-resource-group': 'bf-common-rg',
        'warmup-url': 'https://api.bannerflow.com',
        'warmup-path': '/studio/'
      },
      'production_northeurope': {
      }
    }

# everything below here should be generic
jobs:
  test:
    name: Run tests and sonar
    permissions:
      actions: read
      contents: read
      id-token: write
      checks: write
      pull-requests: write
      packages: read
    runs-on: ubuntu-latest
    timeout-minutes: 13

    env:
      AzureServiceBusSettings_Prefix: "ci_${{github.run_id}}"

    steps:
      - uses: actions/checkout@v4
        if: github.ref != format('refs/heads/{0}', github.event.repository.default_branch)

      - name: Get Release info
        if: github.ref != format('refs/heads/{0}', github.event.repository.default_branch)
        id: release-info
        uses: nordicfactory/shared-github-actions/slack/create-build-info@main

      - name: Create sonar project if needed
        if: github.ref != format('refs/heads/{0}', github.event.repository.default_branch)
        uses: nordicfactory/shared-github-actions/sonar/create-project@main
        with:
          token: ${{ secrets.SONAR_TOKEN }}
          project-key: ${{ env.SONAR_PROJECT_KEY }}
          project-name: ${{ env.NAME }}

      - name: Build
        if: github.ref != format('refs/heads/{0}', github.event.repository.default_branch)
        id: build
        uses: nordicfactory/shared-github-actions/build/dotnet@main
        with:
          test-reporter: EnricoMi
          base-directory: ${{ env.BASE_DIRECTORY }}
          sonar-token: ${{ secrets.SONAR_TOKEN }}
          only-build-on-change: true
          dotnet-version: "9.0.x"

      - name: Send slack fail message
        id: send-build-failed
        if: failure() && github.ref != format('refs/heads/{0}', github.event.repository.default_branch)
        uses: nordicfactory/shared-github-actions/slack/send-message@main
        with:
          channel-id: ${{ env.BUILD_CHANNEL_ID }}
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          message: "${{ env.MESSAGE_PREFIX }}${{ env.BUILD_DESCRIPTION }} <${{ steps.build.outputs.url_html }}|test results> ❌"

  build:
    name: Build docker image
    permissions:
      actions: read
      contents: read
      id-token: write
      packages: read
    runs-on: ubuntu-latest
    timeout-minutes: 25
    if: github.actor != 'dependabot[bot]'
    steps:
      - uses: actions/checkout@v4

      - name: Get Release info
        id: release-info
        uses: nordicfactory/shared-github-actions/slack/create-build-info@main

      - name: Azure login
        uses: nordicfactory/shared-github-actions/azure/login@main
        with:
          client-id: ${{ secrets.AZURE_DEPLOY_CLIENT_ID }}
          client-secret: ${{ secrets.AZURE_DEPLOY_CLIENT_SECRET }}
          tenant-id: ${{ secrets.AZURE_DEPLOY_TENANT_ID }}
          subscription-id: ${{ env.AZURE_DEV_SUBSCRIPTION_ID }}

      - name: Docker build
        uses: nordicfactory/shared-github-actions/build/docker@main
        with:
          base-directory: ${{ env.BASE_DIRECTORY }}
          container-name: ${{ env.CONTAINER_NAME }}

      - name: Send slack fail message
        id: send-docker-build-failed
        if: failure()
        uses: nordicfactory/shared-github-actions/slack/send-message@main
        with:
          channel-id: ${{ env.BUILD_CHANNEL_ID }}
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          message: "${{ env.MESSAGE_PREFIX }}${{ env.BUILD_DESCRIPTION }} failed ❌"

  prepare-deploy:
    needs:
      - build
      - test
    name: Prepare deployment
    runs-on: ubuntu-latest
    timeout-minutes: 25
    outputs:
      message-state: ${{ steps.send-deploy-started.outputs.state }}

    if: ${{ github.actor != 'dependabot[bot]' }}
    steps:
      - uses: actions/checkout@v4

      - name: Get Release info
        id: release-info
        uses: nordicfactory/shared-github-actions/slack/create-build-info@main

      - name: Send initial deploy message
        uses: nordicfactory/shared-github-actions/deploy-message/prepare-deploy-message@main
        id: send-deploy-started
        with:
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          message: "${{ env.MESSAGE_PREFIX }}${{ env.DEPLOYMENT_DESCRIPTION }}: Starting::loading-dots:"
          sandbox-channel: "releases-sandbox"
          production-channel: "releases"

  deploy:
    needs:
      - prepare-deploy
      - build
      - test
    name: Deploy to ${{ matrix.environment }} ${{ matrix.region }}

    permissions:
      actions: read
      contents: read
      id-token: write
      checks: write
      packages: read
      pull-requests: write
    runs-on: ubuntu-latest
    timeout-minutes: 25
    concurrency:
      group: ${{ matrix.environment }}_${{ matrix.region }}
      cancel-in-progress: false
    if: ${{ github.actor != 'dependabot[bot]' }}
    strategy:
      matrix:
        environment: [sandbox, production]
        region: [westeurope, northeurope]
        is-main:
          - ${{ github.ref == format('refs/heads/{0}', github.event.repository.default_branch) }}
        exclude:
          - is-main: false
            environment: production
          - environment: sandbox
            region: northeurope
      fail-fast: false
    environment:
      name: ${{ matrix.environment }}_${{ matrix.region }}
    steps:
      - uses: actions/checkout@v4

      - name: Setup PR Environment
        id: pr-name
        uses: nordicfactory/shared-github-actions/github/create-name@main
        with:
          name: ${{ env.NAME }}
          create-branch-specific-name: ${{ env.PR_ENVIRONMENTS && github.event_name != 'workflow_dispatch'}}

      - name: Set environment variables
        id: set-vars
        uses: nordicfactory/shared-github-actions/azure/defaults@main
        with:
          environment-region: ${{ matrix.environment }}_${{ matrix.region }}
          overrides: ${{ env.ENV_OVERRIDES }}
          service-name: ${{ env.NAME }}
          name-prefix: ${{ steps.pr-name.outputs.prefix }}

      - name: parse messages
        uses: nordicfactory/shared-github-actions/deploy-message/parse-message@main
        id: parse-message-state
        with:
          message-state: ${{ needs.prepare-deploy.outputs.message-state }}
          environment: ${{ matrix.environment }}

      - name: Azure login
        uses: nordicfactory/shared-github-actions/azure/login@main
        with:
          client-id: ${{ secrets.AZURE_DEPLOY_CLIENT_ID }}
          client-secret: ${{ secrets.AZURE_DEPLOY_CLIENT_SECRET }}
          tenant-id: ${{ secrets.AZURE_DEPLOY_TENANT_ID }}
          subscription-id: ${{ steps.set-vars.outputs.subscription-id }}

      - name: Set secret variables
        uses: nordicfactory/shared-github-actions/secrets/set@main
        with:
          keyvault-name: ${{ steps.set-vars.outputs.primary-keyvault-name }}
          application-name: ${{ env.NAME }}
          region: ${{ matrix.region }}

      - name: Get Release info
        id: release-info
        uses: nordicfactory/shared-github-actions/slack/create-build-info@main

      - name: Send deploy start slack message
        uses: nordicfactory/shared-github-actions/slack/send-message@main
        id: send-deploy-started
        with:
          channel-id: ${{ steps.parse-message-state.outputs.channel-id }}
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          thread-ts: ${{ steps.parse-message-state.outputs.ts }}
          message: "${{ matrix.region }}: Starting::loading-dots:"

      - name: Create resource group name
        uses: nordicfactory/shared-github-actions/azure/resource-group-name@main
        id: resource-group-name
        with:
          environment: ${{ matrix.environment }}
          region: ${{ matrix.region }}
          resource-group-prefix: ${{ env.APP_RESOURCE_GROUP_PREFIX }}

      - name: Deploy infrastructure
        uses: nordicfactory/shared-github-actions/deploy/bicep@main
        id: infrastructure
        with:
          deployment-name: ${{ env.NAME }}-${{ steps.set-vars.outputs.aks-cluster-name }}-infra
          subscription-id: ${{ steps.set-vars.outputs.subscription-id }}
          resource-group: ${{ steps.resource-group-name.outputs.name }}
          region: ${{ matrix.region }}
          environment: ${{ matrix.environment }}
          parameters: managedIdentityName=${{ steps.set-vars.outputs.managed-identity-name }} managedFederatedName=${{steps.set-vars.outputs.managed-federated-name}} serviceName=${{ steps.pr-name.outputs.name }} namespace=${{ env.NAMESPACE }} aksClusterName=${{ steps.set-vars.outputs.aks-cluster-name }} aksClusterResourceGroup=${{ steps.set-vars.outputs.aks-resource-group }}
          tags: "feature=${{ env.FEATURE }}"
          only-run-on-change: false

      - name: Lookup identity id
        uses: nordicfactory/shared-github-actions/azure/identity-lookup@main
        id: lookup-identity
        with:
          identity-name: ${{ steps.set-vars.outputs.managed-identity-name }}

      - name: Assign role access to key vault
        uses: nordicfactory/shared-github-actions/azure/role-assign/keyvault/secrets@main
        with:
          keyvault-name: ${{ steps.set-vars.outputs.primary-keyvault-name }}
          principal-id: ${{ steps.lookup-identity.outputs.principal-id }}

      - name: Assign role access to storage account
        uses: nordicfactory/shared-github-actions/azure/role-assign/storageaccount@main
        with:
          storage-account-name: ${{ env.storage-account }}
          storage-account-resource-group: ${{ env.storage-account-resource-group }}
          storage-account-role: "Storage Blob Data Contributor"
          principal-id: ${{ steps.lookup-identity.outputs.principal-id }}

      - name: Assign role access to auth0 key vault
        uses: nordicfactory/shared-github-actions/azure/role-assign/keyvault/secrets@main
        with:
          keyvault-name: ${{ env.auth0-keyvault-name }}
          principal-id: ${{ steps.lookup-identity.outputs.principal-id }}

      - name: Assign role access to service bus
        uses: nordicfactory/shared-github-actions/azure/role-assign/servicebus@main
        with:
          service-bus-name: ${{ env.servicebus }}
          service-bus-resource-group: ${{ env.servicebus-resource-group }}
          principal-id: ${{ steps.lookup-identity.outputs.principal-id }}

      - name: Send deploy start slack message
        uses: nordicfactory/shared-github-actions/slack/send-message@main
        with:
          ts: ${{ steps.send-deploy-started.outputs.ts }}
          channel-id: ${{ steps.parse-message-state.outputs.channel-id }}
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          message: "${{ matrix.region }}: Deploying:loading-dots:"

      - name: Create secrets
        uses: nordicfactory/shared-github-actions/aks/create-secrets@main
        with:
          environment: ${{ matrix.environment }}

      - name: Deploy to aks
        uses: nordicfactory/shared-github-actions/deploy/aks@main
        with:
          chart: microservice-chart
          name: ${{ steps.pr-name.outputs.name }}
          namespace: ${{ env.NAMESPACE }}
          image: "bannerflow.azurecr.io/${{ env.CONTAINER_NAME }}"
          resource-group: ${{ steps.set-vars.outputs.aks-resource-group }}
          cluster-name: ${{ steps.set-vars.outputs.aks-cluster-name }}
          environment: ${{ matrix.environment }}
          environment-region: ${{ matrix.environment }}_${{ matrix.region }}
          managed-identity: ${{ steps.lookup-identity.outputs.client-id }}
          extra-parameters: "env.AZURE_REGION=${{ matrix.region }} ingressPrefixPath=${{ steps.pr-name.outputs.ingress-path }} env.IS_PR_ENVIRONMENT=${{ steps.pr-name.outputs.is-pr-environment }} env.PR_ENVIRONMENT_NAME=${{ steps.pr-name.outputs.prefix }} prefixName=${{ steps.pr-name.outputs.prefix }}"
          timeout: 10m

      - name: Run Smoke Test
        id: smoke-test
        uses: nordicfactory/shared-github-actions/rest@main
        with:
          warmup-url: "${{ env.warmup-url }}${{ steps.pr-name.outputs.ingress-path }}${{ env.warmup-path }}"
          files: ${{ env.BASE_DIRECTORY }}/${{ env.BASE_DIRECTORY }}.Test.Integration/smoke-tests.http
          env_file: ${{ env.BASE_DIRECTORY }}/${{ env.BASE_DIRECTORY }}.Test.Integration/http-client.env.json
          env-variables: "prefix-path=${{ steps.pr-name.outputs.ingress-path }}"
          environment: ${{ matrix.environment }}
          test-reporter: EnricoMi

      - name: Comment PR with environment URL
        if: env.PR_ENVIRONMENTS == 'true' && github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const prNumber = context.issue.number;
            const commentBody = `PR Environment is ready: ${{ env.warmup-url }}${{ steps.pr-name.outputs.ingress-path }}${{ env.warmup-path }}`;

            github.rest.issues.createComment({
              issue_number: prNumber,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: commentBody,
            });

      - name: Report deployment status
        if: always()
        uses: nordicfactory/shared-github-actions/deploy-message/report-deployment-status@main
        with:
          status: ${{ job.status }}
          smoke-test-outcome: ${{ steps.smoke-test.outcome }}
          ts: ${{ steps.send-deploy-started.outputs.ts }}
          reaction-ts: ${{ steps.parse-message-state.outputs.ts }}
          channel-id: ${{ steps.parse-message-state.outputs.channel-id }}
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          success-message: "${{ matrix.region }}: Deployed 🗸"
          failure-message: "${{ matrix.region }}: Failed ❌"
          smoke-test-failure-message: "${{ matrix.region }}: <${{ steps.smoke-test.outputs.test-report }}|Smoke tests> failed ❌"
          cancelled-message: "${{ matrix.environment }}: Cancelled 🛇"
          region: ${{ matrix.region }}

  prepare-done:
    needs:
      - prepare-deploy
      - deploy
    name: Post deployment
    runs-on: ubuntu-latest
    if: always()
    steps:
      - uses: actions/checkout@v4

      - name: Get Release info
        id: release-info
        uses: nordicfactory/shared-github-actions/slack/create-build-info@main

      - name: Report deployment to new relic
        uses: nordicfactory/shared-github-actions/new-relic/create-deployment@main
        if: github.ref == format('refs/heads/{0}', github.event.repository.default_branch)
        with:
          application-name: ${{ env.NAME }}
          NEW_RELIC_API_KEY: ${{ env.secret-newrelic-api-key }}
          description: "Deployment finished: ${{ needs.deploy.result }}"
          environment: "production"
          version: ${{ steps.release-info.outputs.commit-message }}
          changelog: ${{ steps.release-info.outputs.build-url}}

      - name: send done deploy message
        uses: nordicfactory/shared-github-actions/deploy-message/done-deploy-message@main
        with:
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          success-message: "${{ env.MESSAGE_PREFIX }}${{ env.DEPLOYMENT_DESCRIPTION }}: Done 🗸"
          failure-message: "${{ env.MESSAGE_PREFIX }}${{ env.DEPLOYMENT_DESCRIPTION }}: Failed ❌"
          cancelled-message: "${{ env.MESSAGE_PREFIX }}${{ env.DEPLOYMENT_DESCRIPTION }}: Cancelled 🛇"
          status: ${{ needs.deploy.result }}
          state: ${{ needs.prepare-deploy.outputs.message-state }}
