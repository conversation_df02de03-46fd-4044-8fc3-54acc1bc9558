name: Run database migration

on:
  workflow_dispatch:
    inputs:
      step:
        description: 'describe the step'
        required: true

env:
  GITHUB_TOKEN: ${{ github.token }}
  BASE_DIRECTORY: "Studio"

jobs:
  generate-script:
    runs-on: ubuntu-latest
    env:
      AzureServiceBusSettings_Prefix: "ci_${{github.run_id}}"

    steps:
      - uses: actions/checkout@v4
      
      - name: Create a service bus prefix
        run: |
          mkdir -p /home/<USER>/.bannerflow
          echo "{\"Prefix\": \"${AzureServiceBusSettings_Prefix}\"}" > /home/<USER>/.bannerflow/serviceBusPrefixConfiguration.json

      - name: Setup .NET
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '9.0.x'

      - name: Restore dependencies
        run: dotnet restore Studio/Studio/Studio.csproj

      - name: Build dependencies
        run: dotnet build Studio/Studio/Studio.csproj --no-restore -c Release

      - name: Install EF Tools
        run: dotnet tool install --global dotnet-ef

      - name: Generate migration script
        run: |
          dotnet ef migrations script -o migration.sql -s Studio/Studio/Studio.csproj -p Studio/Studio.Persistence/Studio.Persistence.csproj -c StudioContext --idempotent --configuration Release --no-build

      
      - name: Upload SQL script as artifact
        uses: actions/upload-artifact@v4
        with:
          name: migration-script
          path: migration.sql


  update-database-sandbox:
    needs: generate-script
    runs-on: ubuntu-latest

    steps:
      - name: Download SQL script artifact
        uses: actions/download-artifact@v4
        with:
          name: migration-script

    env:
      SERVER: bf-studiosql-sandbox.database.windows.net,1433
      DATABASE: Studio
      USER: ${{ secrets.STUDIO_SANDBOX_DATABASE_USER }}
      PASSWORD: ${{ secrets.STUDIO_SANDBOX_DATABASE_PASSWORD }}

      # - name: update database
      #   run: |
      #       content=$(cat migration.sql)
      #       echo "File content: $content"
      #       echo "content=$content" >> $GITHUB_OUTPUT

      # - name: Execute SQL script
      #   env:
      #     CONNECTION_STRING: ${{ github.event.inputs.connectionString }}
      #   run: |
      #     dotnet tool install --global dotnet-sqlcli
      #     sqlcmd -S $SERVER -d $DATABASE -U $USER -P $PASSWORD -i migration.sql



