name: Delete Deployment

concurrency:
  group: ${{ github.head_ref || github.ref_name }}

env:
  CONTAINER_NAME: "studio-api"
  NAMESPACE: "studio"
  NAME: "studioapi"
  APP_RESOURCE_GROUP_PREFIX: "bf-Studio"
  PR_ENVIRONMENTS: "true"

on:
  pull_request:
    types: [closed]
  workflow_dispatch:

jobs:
  check-for-delete:
    if: github.event_name == 'pull_request' && github.event.action == 'closed'
    runs-on: ubuntu-latest
    name: Check if we should delete the deployment
    outputs:
      delete-deployment: ${{ steps.check_deployment.outputs.delete-deployment }}
    steps:
      - name:
        id: check_deployment
        run: |
          echo "delete-deployment=$PR_ENVIRONMENTS" >> $GITHUB_OUTPUT

  delete-deployment:
    if: ${{ needs.check-for-delete.outputs.delete-deployment == 'true' }}
    runs-on: ubuntu-latest
    name: Delete Deployment
    needs: check-for-delete
    steps:
      - name: Create branch specific name
        id: pr-name
        uses: nordicfactory/shared-github-actions/github/create-name@main
        with:
          name: ${{ env.NAME }}
          create-branch-specific-name: ${{ env.PR_ENVIRONMENTS }}

      - name: Set environment variables
        id: set-vars
        uses: nordicfactory/shared-github-actions/azure/defaults@main
        with:
          environment-region: sandbox_westeurope
          service-name: ${{ env.NAME }}
          name-prefix: ${{ steps.pr-name.outputs.prefix }}

      - name: Create resource group name
        uses: nordicfactory/shared-github-actions/azure/resource-group-name@main
        id: resource_group_name
        with:
          environment: ${{ steps.set-vars.outputs.environment }}
          region: ${{ steps.set-vars.outputs.region }}
          resource-group-prefix: ${{ env.APP_RESOURCE_GROUP_PREFIX }}

      - name: Azure login
        uses: nordicfactory/shared-github-actions/azure/login@main
        with:
          client-id: ${{ secrets.AZURE_DEPLOY_CLIENT_ID }}
          client-secret: ${{ secrets.AZURE_DEPLOY_CLIENT_SECRET }}
          tenant-id: ${{ secrets.AZURE_DEPLOY_TENANT_ID }}
          subscription-id: ${{ steps.set-vars.outputs.subscription-id }}

      - name: Set up kubelogin for non-interactive login
        uses: azure/use-kubelogin@v1
        with:
          kubelogin-version: "v0.0.25"

      - name: setup helm
        uses: azure/setup-helm@v4

      - name: setup kubectl
        uses: azure/setup-kubectl@v4

      - name: Get K8s context
        uses: azure/aks-set-context@v4
        with:
          resource-group: ${{ steps.set-vars.outputs.aks-resource-group }}
          cluster-name: ${{ steps.set-vars.outputs.aks-cluster-name }}
          admin: "false"
          use-kubelogin: "true"

      - name: Delete resources
        uses: nordicfactory/shared-github-actions/github/delete-deployment@main
        with:
          release-name: ${{ steps.pr-name.outputs.name }}
          namespace: ${{ env.NAMESPACE }}
          resource-group: ${{ steps.resource_group_name.outputs.name }}
          managed-identity-name: ${{ steps.set-vars.outputs.managed-identity-name }}
          managed-federated-name: ${{ steps.set-vars.outputs.managed-federated-name }}
        continue-on-error: true
