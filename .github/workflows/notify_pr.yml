name: PR and PR Review Notification.

on:
  pull_request:
    types: [opened, ready_for_review]
  pull_request_review:
    types: [submitted]

env:
  SLACK_PR_CHANNEL: "studio-api-pull-requests"
  SLACK_PR_CHANNEL_DEPENDABOT: "studio-api-pull-requests"

jobs:
  notify-slack:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Notify PR team
        if: ${{ github.actor != 'dependabot[bot]' && github.event.pull_request.draft == false }}
        uses: nordicfactory/shared-github-actions/slack/notify-pr@main
        with:
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          channel-id: ${{ env.SLACK_PR_CHANNEL }}

      - name: Notify PR dependabot
        if: ${{ github.actor == 'dependabot[bot]' }}
        uses: nordicfactory/shared-github-actions/slack/notify-pr@main
        with:
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          channel-id: ${{ env.SLACK_PR_CHANNEL_DEPENDABOT }}
