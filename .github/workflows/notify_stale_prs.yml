name: Notify stale prs

on:
  schedule:
    - cron: "0 7 * * 1-5"

env:
  SLACK_PR_CHANNEL: "studio-api-pull-requests"

jobs:
  notify-slack:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: read
      contents: read
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Notify stale PRs team
        uses: nordicfactory/shared-github-actions/slack/notify-stale-prs@main
        with:
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          channel-id: ${{ env.SLACK_PR_CHANNEL }}
