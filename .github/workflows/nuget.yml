name: Publish NuGet

on:
  push:
    branches:
      - main
    paths:
      - "Studio/Studio.Contracts/**/*"
      - ".github/workflows/nuget.yml"
      - "Studio/Studio.Contracts/Studio.Contracts.csproj"
  workflow_dispatch:
  pull_request:
    types: [opened, synchronize]
    paths:
      - "Studio/Studio.Contracts/**/*"
      - ".github/workflows/nuget.yml"
      - "Studio/Studio.Contracts/Studio.Contracts.csproj"

env:
  AZURE_ARTIFACTS_FEED_URL: https://pkgs.dev.azure.com/bannerflow/_packaging/Bannerflow/nuget/v3/index.json

jobs:
  build-and-publish:
    permissions:
      actions: read
      contents: read
      id-token: write
      checks: write
      packages: write
      # if you need to build a prerelease version of the Contracts library, then comment the line below.
    if: github.ref == format('refs/heads/{0}', github.event.repository.default_branch)
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Publish
        id: publish
        uses: nordicfactory/shared-github-actions/nuget@main
        with:
          project-file: "./Studio/Studio.Contracts/Studio.Contracts.csproj"
