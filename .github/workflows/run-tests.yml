name: Run Tests

concurrency:
  group: ${{ github.head_ref || github.ref_name }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

on:
  workflow_dispatch:
    inputs:
      test_filter:
        description: 'Test filter'
        required: false
        default: 'FullyQualifiedName~Studio.Test.Integration'

env:
  CONTAINER_NAME: "studio-api"
  NAMESPACE: "studio"
  NAME: "studioapi"
  BASE_DIRECTORY: "Studio"
  SONAR_PROJECT_KEY: "studio-api"
  FEATURE: "studio"
  BUILD_CHANNEL_ID: "studio-api-pull-requests"
  APP_RESOURCE_GROUP_PREFIX: "bf-Studio"
  PR_ENVIRONMENTS: "true"
  MESSAGE_PREFIX: ""
  DISABLE_SONAR: "true"

  # these are setup so they appear to be defined, they are set per environment later on
  BUILD_DESCRIPTION: ""
  DEPLOYMENT_DESCRIPTION: ""
  COMMIT_MESSAGE: ""
  secret-newrelic-api-key: ""
  warmup-endpoint: ""
  AZURE_DEV_SUBSCRIPTION_ID: "b736d7b3-0d45-4c37-9174-e671b53a6d83"
  auth0-keyvault-name: ""
  storage-account: ""
  storage-account-resource-group: ""
  servicebus: ""
  servicebus-resource-group: ""
  warmup-url: ""
  warmup-path: ""
  ENV_OVERRIDES: |
    {
      'sandbox': {
        'auth0-keyvault-name': 'bf-auth0-sandbox-kv',
        'storage-account': 'bannerflowsandbox',
        'storage-account-resource-group': 'bf-shared-sandbox-rg',
        'servicebus': 'bf-common-sandbox-asb',
        'servicebus-resource-group': 'bf-common-sandbox-rg',
        'warmup-url': 'https://sandbox-api.bannerflow.com',
        'warmup-path': '/studio/'
      },
      'production': {
        'auth0-keyvault-name': 'bf-auth0-kv',
        'storage-account': 'bannerflow',
        'storage-account-resource-group': 'bf-shared-rg',
        'servicebus': 'bf-common-asb',
        'servicebus-resource-group': 'bf-common-rg',
        'warmup-url': 'https://api.bannerflow.com',
        'warmup-path': '/studio/'
      },
      'production_northeurope': {
      }
    }

# everything below here should be generic
jobs:
  test:
    name: Run tests and sonar
    permissions:
      actions: read
      contents: read
      id-token: write
      checks: write
      pull-requests: write
      packages: read
    runs-on: ubuntu-latest
    timeout-minutes: 13

    env:
      AzureServiceBusSettings_Prefix: "ci_${{github.run_id}}"

    steps:
      - uses: actions/checkout@v4
        if: github.ref != format('refs/heads/{0}', github.event.repository.default_branch)

      - name: Get Release info
        if: github.ref != format('refs/heads/{0}', github.event.repository.default_branch)
        id: release-info
        uses: nordicfactory/shared-github-actions/slack/create-build-info@main

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '9.0.x'

      - name: Restore dependencies
        working-directory: ${{ env.BASE_DIRECTORY }}
        env:
          GITHUB_TOKEN: ${{ github.token }}
          DOTNET_SYSTEM_CONSOLE_ALLOW_ANSI_COLOR_REDIRECTION: "true"
          TERM: "xterm-256color"
        run: dotnet restore

      - name: Run tests
        working-directory: ${{ env.BASE_DIRECTORY }}
        run: dotnet test --logger "trx;LogFileName=test-results.trx" --results-directory ./TestResults --filter "${{ github.event.inputs.test_filter }}" --configuration "Release" --collect:"XPlat Code Coverage;Format=opencover" -p:OpenApiDocumentsDirectory="open-api-export"
        shell: bash

      - name: Test Report
        id: test-reporter
        uses: EnricoMi/publish-unit-test-result-action@v2
        with:
          files: |
            **/test-results.trx
          action_fail: true
