﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Studio.Application.Admin.Commands;
using Studio.Common.Constants;
using Studio.Controllers.ErrorHandling.ProblemDetails.CreativeSet;

namespace Studio.Controllers.Admin;

[ApiController]
[Authorize(Policy = AuthorizationPolicies.StudioAdmin)]
[Produces("application/json")]
[Route("api/admin")]
public class AdminController : ControllerBase
{
    private readonly IMediator _mediator;

    public AdminController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpPost]
    [Route("capture-snapshot/{creativeSetId:int}")]
    [ProducesResponseType(typeof(CaptureSnapshotResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(CreativeSetBaseProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> CaptureSnapshot(int creativeSetId, CancellationToken cancellationToken)
    {
        var response = await _mediator.Send(new CaptureSnapshotCommand(creativeSetId), cancellationToken);
        return Ok(response);
    }
}