using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Studio.Application.Assets.Commands.UploadImage;
using Studio.Application.Dtos;
using Studio.Application.Dtos.BrandLibrary;
using Studio.Application.Videos.Commands.UploadVideos;
using Studio.Common.Constants;
using Studio.Controllers.ActionFilters;

namespace Studio.Controllers.Asset;

[ApiController]
[Authorize]
[Route("api/asset")]
[UseNewtonsoftJsonFormatter]
public class AssetController : ControllerBase
{
    private readonly IMediator _mediator;

    public AssetController(IMediator Mediator)
    {
        _mediator = Mediator;
    }

    [HttpPost("upload/video")]
    [ProducesResponseType(typeof(VideoAssetDtoV2), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UploadVideo([FromForm] UploadVideoAssetRequest request)
    {
        await using var videoStream = request.Video.OpenReadStream();

        var result = await _mediator.Send(new UploadVideoAssetCommand(
            request.BrandId,
            request.Video.FileName,
            videoStream,
            request.Video.ContentType));

        return Ok(result);
    }

    [HttpPost("upload/image")]
    [ProducesResponseType(typeof(ImageAssetDtoV2), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UploadImage([FromForm] UploadImageAssetRequest request)
    {
        await using var imageStream = request.Image.OpenReadStream();

        var result = await _mediator.Send(
            new UploadImageAssetCommand(
            request.BrandId,
            request.Image.FileName,
            imageStream,
            request.IsGenAi == "true"
        ));

        return Ok(result);
    }

    [HttpPost("brand-library")]
    [ProducesResponseType(typeof(BrandLibraryDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> AddElementToBrandLibraryCommand([FromHeader(Name = HttpHeader.BrandId)] string brandId, [FromBody] AddElementToBrandLibraryRequest request)
    {
        var result = await _mediator.Send(
            new AddElementToBrandLibraryCommand(brandId, request.OriginalElementId, request.Element));

        return Ok(result);
    }
}