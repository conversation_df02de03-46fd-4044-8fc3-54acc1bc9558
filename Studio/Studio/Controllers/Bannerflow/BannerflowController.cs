using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Studio.Application.Bannerflow.Queries.CreativeSetsForListView;
using Studio.Application.Bannerflow.Queries.GetCreativeSetsForListView;
using Studio.Common.Constants;
using Studio.Persistence.EF.Context;
using Studio.Results;
using CreativeSetForListView = Studio.Application.Bannerflow.Queries.CreativeSetsForListView.CreativeSetForListView;

namespace Studio.Controllers.Bannerflow;

[ApiController]
[Authorize]
[Produces("application/json")]
[Route("api/bannerflow")]
public class BannerflowController : ControllerBase
{
    private readonly IMediator _mediator;

    public BannerflowController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpGet]
    [Route("creativesets/{brandId}/listview")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetCreativeSetsByBrandId(string brandId)
    {
        var result = await _mediator.Send(new GetCreativeSetsForBannerflowQuery(brandId));
        return result.ToIActionResult(Ok);
    }

    [HttpPost]
    [Route("creativeSets/listview")]
    [ProducesResponseType(typeof(IList<CreativeSetForListView>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetCreativeSets(
        [FromHeader(Name = HttpHeader.BrandId)] string brandId,
        CreativeSetsForListViewRequest request)
    {
        var result = await _mediator.Send(new CreativeSetsForListViewQuery(
            request.CreativeSetIds.Select(id => id.ToInt()).ToList(),
            brandId));
        return result.ToIActionResult(Ok);
    }
}