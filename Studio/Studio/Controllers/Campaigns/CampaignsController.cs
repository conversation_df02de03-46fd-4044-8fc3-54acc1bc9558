using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Studio.Application.Campaigns.Queries;

namespace Studio.Controllers.Campaigns;

[ApiController]
[Authorize]
[Produces("application/json")]
[Route("api/campaigns")]
public class CampaignsController : ControllerBase
{
    private readonly IMediator _mediator;

    public CampaignsController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// Given a cretive set id, it returns a list of creatives and campaigns connected to them
    /// </summary>
    /// <param name="creativeSetId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpGet("connected-to/{creativeSetId:int}")]
    [ProducesResponseType(typeof(GetConnectedCampaignsByCreativeSetIdResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetConnectedCampaignsByCreativeSet(
        int creativeSetId,
        CancellationToken cancellationToken)
    {
        var query = new GetConnectedCampaignsByCreativeSetIdQuery(creativeSetId);
        var connectedCampaignsForSet = await _mediator.Send(query, cancellationToken);
        return Ok(connectedCampaignsForSet);
    }
}
