﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Studio.Application.CreativeGeneration;
using Studio.Application.Dtos.CreativeGeneration;

namespace Studio.Controllers.CreativeGeneration;

[ApiController]
[Authorize]
[Produces("application/json")]
[Route("api/creative-generation")]
public class CreativeGenerationController : ControllerBase
{
    private readonly IMediator _mediator;

    public CreativeGenerationController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpPost]
    [Route("generate")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Generate(IList<CreativeGenerationDto> body, CancellationToken cancellationToken)
    {
        var command = new InitCreativeGenerationCommand(body);

        var result = await _mediator.Send(command, cancellationToken);
        return Ok(result);
    }
}