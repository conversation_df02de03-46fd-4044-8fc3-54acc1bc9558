﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Studio.Application.CreativeSets.Commands.DesignsAndVersions.Create;
using Studio.Application.CreativeSets.Commands.DesignsAndVersions.Update;
using Studio.Application.CreativeSets.Commands.DuplicateToExistingCreativeSet;
using Studio.Application.CreativeSets.Commands.UpdateCreativeSetStateId;
using Studio.Application.CreativeSets.Queries;
using Studio.Application.CreativeSets.Queries.CreativeSetSnapshotUrl;
using Studio.Application.CreativeSets.Queries.GetCreativeSet;
using Studio.Application.CreativeSets.Queries.GetCreativeSetsByIds;
using Studio.Application.CreativeSets.Queries.GetCreativeSetsMetadata;
using Studio.Application.Dtos;
using Studio.Application.Dtos.DesignsAndVersions.Create;
using Studio.Application.Dtos.DesignsAndVersions.Update;
using Studio.Application.Meters;
using Studio.Common.Constants;
using Studio.Controllers.CreativeSets.Requests;
using Studio.Controllers.ErrorHandling.ProblemDetails.CreativeSet;
using Studio.Filters;
using Studio.Persistence.EF.Context;
using Studio.Results;
using static Studio.Contracts.Requests.GetCreativeSetsByIds;

namespace Studio.Controllers.CreativeSets;
#nullable enable

[ApiController]
[Authorize]
[Produces("application/json")]
[Route("api/creative-sets")]
public class CreativeSetController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly CreativeSetMetrics _metrics;

    public CreativeSetController(IMediator mediator, CreativeSetMetrics metrics)
    {
        _mediator = mediator;
        _metrics = metrics;
    }

    /// <summary>
    /// This provides creative set and creative count that is displayed in account settings(manage brands).
    /// </summary>
    /// <param name="brandIds"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpPost("count-creative-sets-and-creatives")]
    [ProducesResponseType(typeof(GetCreativeSetAndCreativeCountByBrandIdQueryResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetCreativeSetAndCreativeCountByBrandId(
        IEnumerable<string> brandIds,
        CancellationToken cancellationToken)
    {
        var query = new GetCreativeSetAndCreativeCountByBrandIdQuery(brandIds);
        var creativeSetAndCreativeCount = await _mediator.Send(query, cancellationToken);
        return Ok(creativeSetAndCreativeCount);
    }

    [HttpPost("get-by-ids")]// post instead of get because large number of ids can be passed in the request
    [ProducesResponseType(typeof(GetCreativeSetsByIdsResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetByIds(
        GetCreativeSetsByIdsExternalQuery getCreativeSetsByIdsQuery, CancellationToken cancellationToken)
    {
        var creativeSets = await _mediator.Send(getCreativeSetsByIdsQuery, cancellationToken);
        if (creativeSets is null)
        {
            return NoContent();
        }

        return Ok(creativeSets);
    }

    [HttpGet]
    [Route("{creativeSetId:int}")]
    [ProducesResponseType(typeof(CreativeSetDtoV2), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(CreativeSetBaseProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetCreativeSet(int creativeSetId, CancellationToken cancellationToken)
    {
        var set = await _mediator.Send(new GetCreativeSetQuery(creativeSetId), cancellationToken);
        return Ok(set);
    }

    [HttpGet]
    [Route("{creativeSetId:int}/last-modified")]
    [ProducesResponseType(typeof(DateTime?), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(CreativeSetBaseProblemDetails), StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> GetCreativeSetLastModified(int creativeSetId, CancellationToken cancellationToken)
    {
        var lastModified = await _mediator.Send(new GetCreativeSetLastModifiedQuery(creativeSetId), cancellationToken);
        return Ok(new { lastModified });
    }

    /// <summary>
    /// Endpoint for updating designs and versions of a creative set.
    /// </summary>
    /// <param name="creativeSetId"></param>
    /// <param name="updateDesignsAndVersions"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpPut]
    [Route("{creativeSetId:int}/designs-and-versions")]
    [ProducesResponseType(typeof(CreativeSetDtoV2), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [UserActivityFilter]
    public async Task<IActionResult> UpdateDesignsAndVersions(int creativeSetId, UpdateDesignsAndVersionsDto updateDesignsAndVersions, CancellationToken cancellationToken)
    {
        using var timer = _metrics.RecordUpdateEvent(creativeSetId);
        var set = await _mediator.Send(new UpdateDesignsAndVersionsCommand(creativeSetId, updateDesignsAndVersions),
            cancellationToken);
        return Ok(set);
    }

    [HttpPut]
    [Route("{creativeSetId:int}/designs-and-versions/restore")]
    public async Task<IActionResult> RestoreBackup(
        CreativeSetDtoV2 creativeSetDtoV2,
        [FromHeader(Name = HttpHeader.UserId)] string? userId,
        CancellationToken cancellationToken)
    {
        var set = await _mediator.Send(new RestoreDesignsAndVersionsCommand(creativeSetDtoV2, userId, false),
            cancellationToken);
        return Ok(set);
    }

    [HttpPut]
    [Route("{creativeSetId:int}/designs-and-versions/test_restore")]
    public async Task<IActionResult> TestRestoreBackup(
        CreativeSetDtoV2 creativeSetDtoV2,
        [FromHeader(Name = HttpHeader.UserId)] string userId,
        CancellationToken cancellationToken)
    {
        var set = await _mediator.Send(new RestoreDesignsAndVersionsCommand(creativeSetDtoV2, userId, true),
            cancellationToken);
        return Ok(set);
    }

    [HttpPost]
    [Route("{creativeSetId:int}/designs-and-versions")]
    [ProducesResponseType(typeof(CreativeSetDtoV2), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [UserActivityFilter]
    public async Task<IActionResult> CreateDesignsAndVersions(int creativeSetId, CreateDesignsAndVersionsDto createDesignsAndVersions, CancellationToken cancellationToken)
    {
        using var timer = _metrics.RecordCreateEvent(creativeSetId);
        var set = await _mediator.Send(new CreateDesignsAndVersionsCommand(creativeSetId, createDesignsAndVersions),
                       cancellationToken);
        return Ok(set);
    }


    [HttpPut]
    [Route("{creativeSetId:int}/designs-and-versions/validate")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    public async Task<IActionResult> ValidateDesignsAndVersions(int creativeSetId, UpdateDesignsAndVersionsDto updateDesignsAndVersions, CancellationToken cancellationToken)
    {
        await _mediator.Send(new ValidateDesignsAndVersionsCommand(creativeSetId, updateDesignsAndVersions),
            cancellationToken);
        return Ok();
    }

    [HttpPost]
    [Route("{sourceCreativeSetId}/duplicate-to-existing/{targetCreativeSetId}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DuplicateToExistingCreativeSet(
        [FromHeader(Name = HttpHeader.BrandId)] string brandId,
        string sourceCreativeSetId,
        string targetCreativeSetId,
        DuplicateToExistingCreativeSetRequest body,
        CancellationToken cancellationToken)
    {
        var command = new DuplicateToExistingCreativeSetCommand
        {
            BrandId = brandId,
            SourceCreativeSetId = sourceCreativeSetId.ToInt(),
            TargetCreativeSetId = targetCreativeSetId.ToInt(),
            SourceSizeIds = body.SourceSizeIds.Select(s => s.ToInt()),
        };

        var result = await _mediator.Send(command, cancellationToken);
        return result.ToIActionResult(Ok);
    }

    [HttpGet]
    [Route("{creativeSetId}/snapshots/{snapshotId}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> CreativeSetSnapshot(
        int creativeSetId,
        int snapshotId,
        CancellationToken cancellationToken)
    {
        var query = new CreativeSetSnapshotUrlQuery(creativeSetId, snapshotId);

        var result = await _mediator.Send(query, cancellationToken);
        return result.ToIActionResult(Ok);
    }

    [HttpPut]
    [Route("{creativeSetId}/state-id")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateCreativeSetStateId(UpdateCreativeSetStateIdDto body, CancellationToken cancellationToken)
    {
        var command = new UpdateCreativeSetStateIdCommand
        {
            StateId = body.StateId,
            CreativeSetId = body.CreativeSetId,
            CreativesToUpdate = body.CreativesToUpdate
        };

        var result = await _mediator.Send(command, cancellationToken);
        return Ok(result);
    }

    [HttpPost("load-metadata")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<IActionResult> GetCreativeSetsMetadata(
        GetCreativeSetsMetadataQuery getCreativesMetadataQuery,
        CancellationToken cancellationToken)
    {
        var creativeSets = await _mediator.Send(getCreativesMetadataQuery, cancellationToken);
        if (creativeSets is null || creativeSets.CreativeSetsMetadata.Length == 0)
        {
            return NoContent();
        }

        return Ok(creativeSets);
    }

    /// <summary>
    /// Updates version information without taking version properties into consideration.
    /// </summary>
    /// <param name="creativeSetId"></param>
    /// <param name="versionId"></param>
    /// <param name="updateVersionInfoDto"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpPut]
    [Route("{creativeSetId:int}/versions/{versionId:int}")]
    [ProducesResponseType(typeof(VersionInfoDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateVersionInfo(int creativeSetId, int versionId, UpdateVersionInfoRequest request, CancellationToken cancellationToken)
    {
        var version = await _mediator.Send(new UpdateVersionInfoCommand(creativeSetId, versionId, request.Version, request.Creatives),
            cancellationToken);

        return version.ToIActionResult(Ok);
    }
}