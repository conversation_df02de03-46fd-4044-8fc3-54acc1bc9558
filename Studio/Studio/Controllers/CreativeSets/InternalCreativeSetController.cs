﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Studio.Application.CreativeSets.Commands.DesignsAndVersions.Create;
using Studio.Application.CreativeSets.Commands.DesignsAndVersions.Update;
using Studio.Application.CreativeSets.Commands.DuplicateToExistingCreativeSet;
using Studio.Application.CreativeSets.Commands.UpdateCreativeSetStateId;
using Studio.Application.CreativeSets.Queries;
using Studio.Application.CreativeSets.Queries.CreativeSetSnapshotUrl;
using Studio.Application.CreativeSets.Queries.GetCreativeSet;
using Studio.Application.CreativeSets.Queries.GetCreativeSetsByIds;
using Studio.Application.CreativeSets.Queries.GetCreativeSetsMetadata;
using Studio.Application.Dtos;
using Studio.Application.Dtos.DesignsAndVersions.Create;
using Studio.Application.Dtos.DesignsAndVersions.Update;
using Studio.Application.Meters;
using Studio.Common.Constants;
using Studio.Controllers.CreativeSets.Requests;
using Studio.Controllers.ErrorHandling.ProblemDetails.CreativeSet;
using Studio.Filters;
using Studio.Persistence.EF.Context;
using Studio.Results;
using static Studio.Contracts.Requests.GetCreativeSetsByIds;

namespace Studio.Controllers.CreativeSets;
#nullable enable

/// <summary>
/// This controller is intended to be used by any internal service (meaning backend service). The reason why
/// we segregate between them and endpoints used by our frontend web clients is that the underlying implementation
/// differs. Specifically when it comes to authorization. We trust our internal services to a higher degree.
/// </summary>
[ApiController]
[Authorize]
[Produces("application/json")]
[Route("api/internal/creative-sets")]
public class InternalCreativeSetController(IMediator mediator) : ControllerBase
{
    [HttpPost("get-by-ids")]// post instead of get because large number of ids can be passed in the request
    [ProducesResponseType(typeof(GetCreativeSetsByIdsResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetCreativeSetsByIds(
        GetCreativeSetsByIdsInternalQuery getCreativeSetsByIdsInternalQuery, CancellationToken cancellationToken)
    {
        var creativeSets = await mediator.Send(getCreativeSetsByIdsInternalQuery, cancellationToken);
        if (creativeSets is null)
        {
            return NoContent();
        }

        return Ok(creativeSets);
    }
}