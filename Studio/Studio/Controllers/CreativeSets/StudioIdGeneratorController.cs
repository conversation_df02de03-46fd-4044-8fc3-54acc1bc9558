﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Studio.Application.Common.Interfaces;
using Studio.Domain.Entities;

namespace Studio.Controllers.CreativeSets;

// this endpoint is only used by the DAPI !!!

public record CreateSize(int CreativesetId);
public record CreateVersion(int CreativesetId);
public record CreateCreative(int CreativesetId, int SizeId, int VersionId);
public record StudioIdGeneratorResponse(int Id);

[ApiController]
[Authorize]
[Produces("application/json")]
[Route("api/studio-id-generator")]
public class StudioIdGeneratorController(IStudioContext studioContext) : ControllerBase
{
    [HttpPost("size")]
    public async Task<StudioIdGeneratorResponse> CreateSize([FromBody] CreateSize model, CancellationToken cancellationToken)
    {
        var size = new SizeEntity
        {
            CreativesetId = model.CreativesetId,
            Width = 0,
            Height = 0,
            Name = "",
            Deleted = DateTime.UtcNow,
        };

        studioContext.Sizes.Add(size);

        await studioContext.SaveChangesAsync(cancellationToken);

        return new(size.Id);
    }

    [HttpPost("version")]
    public async Task<StudioIdGeneratorResponse> CreateVersion([FromBody] CreateVersion model, CancellationToken cancellationToken)
    {
        var version = new VersionEntity
        {
            CreativesetId = model.CreativesetId,
            Name = "",
            LocalizationId = "",
            TargetUrl = "",
            Deleted = DateTime.UtcNow,
        };

        studioContext.Versions.Add(version);

        await studioContext.SaveChangesAsync(cancellationToken);

        return new(version.Id);
    }

    [HttpPost("creative")]
    public async Task<StudioIdGeneratorResponse> CreateCreative([FromBody] CreateCreative model, CancellationToken cancellationToken)
    {
        var creative = new CreativeEntity
        {
            CreativesetId = model.CreativesetId,
            SizeId = model.SizeId,
            VersionId = model.VersionId,
            DesignId = null,
            TargetUrl = "",
            Checksum = "",
            ApprovalStatus = null,
            Deleted = DateTime.UtcNow,
        };

        studioContext.Creatives.Add(creative);

        await studioContext.SaveChangesAsync(cancellationToken);
        
        return new(creative.Id);
    }
}