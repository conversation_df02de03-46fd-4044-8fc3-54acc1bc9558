﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Studio.Application.Creatives.Queries.GetCreativeDynamicProperties;
using Studio.Application.Creatives.Queries.GetCreativeFeededElements;
using Studio.Application.Creatives.Queries.GetCreativeMetadata;
using Studio.Application.Creatives.Queries.GetCreativesGifFrames;
using Studio.Application.Creatives.Queries.GetCreativesHeavyAssetMetadata;
using Studio.Application.Creatives.Queries.GetCreativesMetadata;
using Studio.Common.Constants;
using Studio.Models;

namespace Studio.Controllers.Creatives;

[Authorize]
[ApiController]
[Produces("application/json")]
public class CreativeController : ControllerBase
{
    private readonly ISender _mediator;

    public CreativeController(ISender mediator)
    {
        _mediator = mediator;
    }

    [HttpGet]
    [Route("api/creatives/{creativeId}/metadata")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetCreativeMetadata(
        int creativeId,
        CancellationToken cancellationToken)
    {
        var query = new GetCreativeMetadataQuery(creativeId);

        return Ok(await _mediator.Send(query, cancellationToken));
    }

    [HttpPost("api/creatives/load-metadata")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<IActionResult> GetCreativesMetadata(
        GetCreativesMetadataRequest request,
        [FromHeader(Name = HttpHeader.BrandId)] string brandId,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(brandId))
        {
            return BadRequest($"{HttpHeader.BrandId} header is required");
        }

        var getCreativesMetadataQuery = new GetCreativesMetadataQuery(request.Ids, brandId);
        var creativeSets = await _mediator.Send(getCreativesMetadataQuery, cancellationToken);
        if (creativeSets is null || creativeSets.CreativesMetadata.Length == 0)
        {
            return NoContent();
        }

        return Ok(creativeSets);
    }

    [HttpGet]
    [Route("api/creatives/{creativeId}/feededelements")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetFeededElements(
        int creativeId,
        [FromHeader(Name = HttpHeader.BrandId)] string brandId,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(brandId))
        {
            return BadRequest($"{HttpHeader.BrandId} header is required");
        }

        var query = new GetCreativeFeededElements(creativeId, brandId);

        var getCreativeFeededElementsQueryResponse = await _mediator.Send(query, cancellationToken);

        return Ok(getCreativeFeededElementsQueryResponse);
    }

    /// <summary> Returns 'problematic' assets metadata for the given creatives.</summary>
    /// <remarks>
    /// They are aggregated by looking into; widget code, widget properties as well as the design document.
    ///  Problematic assets are those that are heavy (too many Megabytes).
    ///  Furthermore they are assets that we do not automatically size optimize.
    ///  This means that large images are not returned as they are filtered through the image optimizer (decreasing their size).
    ///  Videos that are streamed will not be returned either.
    /// </remarks>
    /// <param name="getCreativesHeavyAssetMetadataRequest"></param>
    /// <param name="cancellationToken"></param>
    /// <response code="200">"Problematic" heavy asset(s) founds. View array for more details.</response>
    /// <response code="204">Creatives have no 'problematic' heavy assets</response>
    /// <response code="403">Creative belonging to another brand</response>
    [HttpPost("api/creatives/heavy-asset-metadata")]
    [ProducesResponseType(typeof(GetCreativesHeavyAssetMetadataQueryResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetCreativesHeavyAssetMetadata(
        GetCreativesHeavyAssetMetadataRequest getCreativesHeavyAssetMetadataRequest,
        [FromHeader(Name = HttpHeader.BrandId)] string brandId,
        CancellationToken cancellationToken)
    {
        var query = new GetCreativesHeavyAssetMetadataQuery(getCreativesHeavyAssetMetadataRequest.Ids, brandId);
        var creativesHeavyAssetMetadataQueryResponse = await _mediator.Send(query, cancellationToken);
        if (creativesHeavyAssetMetadataQueryResponse.CreativesHeavyAssetMetadata.Length == 0)
        {
            return NoContent();
        }

        return Ok(creativesHeavyAssetMetadataQueryResponse);
    }

    /// <response code="422">The creative is missing its design</response>
    [HttpGet]
    [Route("api/creatives/{creativeId}/dynamic-properties")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetDynamicProperties(
        int creativeId,
        [FromHeader(Name = HttpHeader.BrandId)] string brandId,
        CancellationToken cancellationToken)
    {
        var query = new GetCreativeDynamicPropertiesQuery(creativeId, brandId);

        var dynamicProperties = await _mediator.Send(query, cancellationToken);

        return Ok(dynamicProperties);
    }

    /// <summary>
    /// Only returns data for creatives with Gif Frames. If no creatives have any Gif Frames, a 204 No Content is returned.
    /// </summary>
    /// <param name="request">id of the creatives</param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [HttpPost("api/creatives/gif-frames")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    public async Task<IActionResult> GetCreativesGifFrames(
        GetCreativesGifFramesQuery request,
        CancellationToken cancellationToken)
    {
        var creativeSets = await _mediator.Send(request, cancellationToken);
        if (creativeSets is null || creativeSets.CreativesGifFrames.Length == 0)
        {
            return NoContent();
        }

        return Ok(creativeSets);
    }
}