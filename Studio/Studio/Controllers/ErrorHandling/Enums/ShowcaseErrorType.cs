﻿using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Studio.Controllers.ErrorHandling.Enums;

[JsonConverter(typeof(StringEnumConverter))]
public enum ShowcaseErrorType
{
    [EnumMember(Value = "showcaseDisabled")]
    ShowcaseDisabled,
    [EnumMember(Value = "showcaseExpired")]
    ShowcaseExpired,
    [EnumMember(Value = "showcaseNotFound")]
    ShowcaseNotFound,
}