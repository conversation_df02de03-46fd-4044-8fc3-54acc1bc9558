﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Studio.Controllers.ErrorHandling.Enums;

namespace Studio.Controllers.ErrorHandling.ProblemDetails.CreativeSet;

public abstract class CreativeSetBaseProblemDetails : Microsoft.AspNetCore.Mvc.ProblemDetails
{
    [Required]
    public abstract ErrorTypes ErrorType { get; }

    [Required]
    [DefaultValue("An error occurred while processing the request")]
    public new string Title => "An error occurred while processing the request";
}