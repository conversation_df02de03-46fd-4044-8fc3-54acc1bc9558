﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Studio.Controllers.ErrorHandling.Enums;

namespace Studio.Controllers.ErrorHandling.ProblemDetails.CreativeSet;

public class CreativeSetNotFoundProblemDetails : CreativeSetBaseProblemDetails
{
    [Required]
    [DefaultValue(ErrorTypes.CreativeSetDeleted)]
    public override ErrorTypes ErrorType => ErrorTypes.CreativeSetNotFound;

    [Required]
    [DefaultValue(StatusCodes.Status404NotFound)]
    public new int Status => StatusCodes.Status404NotFound;

    [Required]
    public new readonly string Detail;

    public CreativeSetNotFoundProblemDetails(string detail)
    {
        Detail = detail;
        // It is necessary to set the Status on the base class. Otherwise (given current implementation) the HttpStatusCode will be incorrect
        base.Status = Status;
    }
}