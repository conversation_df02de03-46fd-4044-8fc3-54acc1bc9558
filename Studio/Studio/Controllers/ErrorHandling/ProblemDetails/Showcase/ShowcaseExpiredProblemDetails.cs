﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Studio.Controllers.ErrorHandling.Enums;

namespace Studio.Controllers.ErrorHandling.ProblemDetails.Showcase;

public class ShowcaseExpiredProblemDetails : ShowcaseBaseProblemDetails
{
    [Required]
    public int CreativeSetId { get; init; }
    [Required]
    public string BrandId { get; init; }

    public ShowcaseExpiredProblemDetails(int creativeSetId, string brandId)
    {
        CreativeSetId = creativeSetId;
        BrandId = brandId;
        // It is necessary to set the Status on the base class. Otherwise (given current implementation) the HttpStatusCode will be incorrect
        base.Status = Status;
    }

    [Required]
    [DefaultValue(ShowcaseErrorType.ShowcaseExpired)]
    public override ShowcaseErrorType ErrorType => ShowcaseErrorType.ShowcaseExpired;

    [Required]
    [DefaultValue(StatusCodes.Status404NotFound)]
    public new int Status => StatusCodes.Status404NotFound;
}