﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Studio.Controllers.ErrorHandling.Enums;

namespace Studio.Controllers.ErrorHandling.ProblemDetails.Showcase;

public class ShowcaseInvalidKeyProblemDetails : ShowcaseValidationBaseProblemDetails
{
    public ShowcaseInvalidKeyProblemDetails()
    {
        // It is necessary to set the Status on the base class. Otherwise (given current implementation) the HttpStatusCode will be incorrect
        base.Status = Status;
    }

    [Required]
    [DefaultValue(ShowcaseValidationErrorType.ShowcaseInvalidKey)]
    public override ShowcaseValidationErrorType ErrorType => ShowcaseValidationErrorType.ShowcaseInvalidKey;

    [Required]
    [DefaultValue(StatusCodes.Status400BadRequest)]
    public new int Status => StatusCodes.Status400BadRequest;
}