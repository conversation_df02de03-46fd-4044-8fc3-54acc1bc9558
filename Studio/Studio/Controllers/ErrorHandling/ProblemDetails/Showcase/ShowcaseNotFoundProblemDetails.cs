﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Studio.Controllers.ErrorHandling.Enums;

namespace Studio.Controllers.ErrorHandling.ProblemDetails.Showcase;

public class ShowcaseNotFoundProblemDetails : ShowcaseBaseProblemDetails
{
    public ShowcaseNotFoundProblemDetails()
    {
        // It is necessary to set the Status on the base class. Otherwise (given current implementation) the HttpStatusCode will be incorrect
        base.Status = Status;
    }

    [Required]
    [DefaultValue(ShowcaseErrorType.ShowcaseNotFound)]
    public override ShowcaseErrorType ErrorType => ShowcaseErrorType.ShowcaseNotFound;

    [Required]
    [DefaultValue(StatusCodes.Status404NotFound)]
    public new int Status => StatusCodes.Status404NotFound;
}