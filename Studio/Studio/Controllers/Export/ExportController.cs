﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Studio.Application.ExportCreativeToVideo;
using Studio.Results;

namespace Studio.Controllers.Export;

[Authorize]
[ApiController]
[Route("api/export")]
public class ExportController : ControllerBase
{
    private readonly IMediator _mediator;

    public ExportController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpPost("video")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> ExportVideo([FromBody] ExportVideoRequest exportVideoRequest)
    {
        var command = new ExportCreativeToVideoCommand
        {
            IsShowcaseExport = false,
            CreativeSetId = exportVideoRequest.CreativeSetId,
            Creatives = exportVideoRequest.Creatives.Select(x => new Application.ExportCreativeToVideo.CreativeToExport
            {
                Id = x.Id,
                Width = x.Width,
                Height = x.Height,
                Duration = x.Duration,
                Fps = x.Fps
            })
                .ToList()
        };

        var result = await _mediator.Send(command);
        return result.ToIActionResult(NoContent);
    }
}
