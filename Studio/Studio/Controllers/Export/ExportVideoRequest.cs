﻿namespace Studio.Controllers.Export;

public record ExportVideoRequest
{
    public required int CreativeSetId { get; init; }
    public required IReadOnlyCollection<CreativeToExport> Creatives { get; init; }
}

public record ShowcaseExportVideoRequest
{
    public required int CreativeSetId { get; init; }
    public required IReadOnlyCollection<CreativeToExport> Creatives { get; init; }
    public required string RecipientEmail { get; init; }
}

public record CreativeToExport
{
    public required int Id { get; init; }
    public required int Width { get; init; }
    public required int Height { get; init; }
    public required decimal Duration { get; init; }
    public required int Fps { get; init; }
}
