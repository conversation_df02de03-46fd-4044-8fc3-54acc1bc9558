﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Studio.Application.Dtos.Export;
using Studio.Application.ExportCreativeToVideo;
using Studio.Application.Showcases.Export;
using Studio.Common.Constants;
using Studio.Results;

namespace Studio.Controllers.Export;

[Authorize(Policy = AuthorizationPolicies.ShowCase)]
[ApiController]
[Route("api/export/showcase")]
public class ShowcaseExportController(IMediator mediator) : ControllerBase
{
    [HttpPost("image")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> ExportImage([FromBody] ShowcaseImageExportDto imageExportRequest)
    {
        var command = new ShowcaseImageExportCommand(imageExportRequest);
        var result = await mediator.Send(command);

        return result.ToIActionResult(NoContent);
    }


    [HttpPost("video")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> ExportVideo([FromBody] ShowcaseExportVideoRequest exportVideoRequest)
    {
        var command = new ExportCreativeToVideoCommand
        {
            IsShowcaseExport = true,
            RecipientEmail = exportVideoRequest.RecipientEmail,
            CreativeSetId = exportVideoRequest.CreativeSetId,
            Creatives = exportVideoRequest.Creatives.Select(x => new Application.ExportCreativeToVideo.CreativeToExport
            {
                Id = x.Id,
                Width = x.Width,
                Height = x.Height,
                Duration = x.Duration,
                Fps = x.Fps
            })
                .ToList()
        };
        var result = await mediator.Send(command);

        return result.ToIActionResult(NoContent);
    }
}
