using GraphQL;
using Studio.Application;
using Studio.Application.Common.Results;
using Studio.Common.Constants;
using Studio.Results;

namespace Studio.Controllers.GraphQl.Extensions;

public static class ExecutionErrorExtensions
{
    private const string BadRequest = "400";
    private const string Forbidden = "403";
    private const string NotFound = "404";

    public static object AddError(this ExecutionErrors errors, string message)
    {
        return errors.AddError(message, null);
    }

    public static object AddError(this ExecutionErrors errors, Result result)
    {
        return errors.AddError(result.Error, result.ErrorType.ToHttpStatus().ToString());
    }

    public static object AddNotFoundError<T>(this ExecutionErrors errors) where T : class
    {
        return errors.AddError(ErrorMessages.NotFound(typeof(T).Name), NotFound);
    }

    public static object AddNotFoundError(this ExecutionErrors errors, string message)
    {
        return errors.AddError(message, NotFound);
    }

    public static object AddForbiddenError(this ExecutionErrors errors, string message = null)
    {
        return errors.AddError(message, Forbidden);
    }

    public static object AddBadRequestError(this ExecutionErrors errors, string message = null)
    {
        return errors.AddError(message, BadRequest);
    }

    private static object AddError(this ExecutionErrors errors, string message, string code)
    {
        var ex = new ExecutionError(message) { Code = code };
        errors.Add(ex);
        TraceTelemetry.Error(ex);
        return null;
    }
}