using Studio.Controllers.GraphQl.Filtering.FilterTypes;
using System.Reflection;

namespace Studio.Controllers.GraphQl.Filtering;

internal static class FilterTypeFactory
{
    public static IFilterType GetFilterType(PropertyInfo property)
    {
        var typeCode = Type.GetTypeCode(property.PropertyType);

        return typeCode switch
        {
            TypeCode.String => new StringFilterType(),
            TypeCode.Object => new ObjectFilterType(),
            _ => throw new InvalidOperationException(
                $"The type {typeCode} is not supported for filtering for the property {property.Name}")
        };
    }
}