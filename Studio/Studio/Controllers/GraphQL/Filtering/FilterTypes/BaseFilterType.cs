using System.Linq.Expressions;

namespace Studio.Controllers.GraphQl.Filtering.FilterTypes;

internal abstract class BaseFilterType<T> : IFilterType
{
    internal readonly IEnumerable<ComparisonOperator> AllowedOperations;

    protected BaseFilterType(IEnumerable<ComparisonOperator> allowedOperations)
    {
        AllowedOperations = allowedOperations;
    }

    /// <summary>
    /// Convertes the filter information into a comaparison Expression.
    /// </summary>
    /// <param name="filterInfo"></param>
    /// <returns></returns>
    public Expression GetFilterExpression(FilterInfo filterInfo)
    {
        if (!IsOperationAllowed(filterInfo.Operation))
        {
            throw new InvalidOperationException(
                $"Filter string is invalid, operation {filterInfo.Operation} is not valid for {filterInfo.Property.Name}");
        }

        switch (filterInfo.Operation)
        {
            case ComparisonOperator.Equals:
            case ComparisonOperator.IsNull:
                return Expression.Equal(filterInfo.AccessExpression, GetValueExpression(filterInfo));
            case ComparisonOperator.NotEquals:
            case ComparisonOperator.NotNull:
                return Expression.NotEqual(filterInfo.AccessExpression, GetValueExpression(filterInfo));
            default:
                throw new InvalidOperationException(
                    $"Error getting expression for filter type, the operation {filterInfo.Operation} is not supported.");
        }
    }

    private Expression GetValueExpression(FilterInfo filterInfo)
    {
        Expression valueExpression = Expression.Convert(Expression.Constant(GetFilterValue(filterInfo.Value)),
            filterInfo.Property.PropertyType);
        return valueExpression;
    }

    protected abstract T GetFilterValue(string value);

    private bool IsOperationAllowed(ComparisonOperator operation)
    {
        return AllowedOperations.Contains(operation);
    }
}