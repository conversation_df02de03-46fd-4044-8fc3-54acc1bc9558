
namespace Studio.Controllers.GraphQl.Filtering.FilterTypes;

internal class ObjectFilterType : BaseFilterType<object>
{
    private static readonly List<ComparisonOperator> ObjectOperations = new() { ComparisonOperator.NotEquals, ComparisonOperator.Equals };

    public ObjectFilterType() : base(ObjectOperations)
    {
    }

    protected override object GetFilterValue(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
        {
            throw new ArgumentException("Filter value is missing");
        }

        if (value.ToLowerInvariant().Equals("null"))
        {
            return null;
        }

        return value;
    }
}