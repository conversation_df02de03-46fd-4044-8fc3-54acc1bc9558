
namespace Studio.Controllers.GraphQl.Filtering.FilterTypes;

internal class StringFilterType : BaseFilterType<string>
{
    private static readonly List<ComparisonOperator> StringOperations = new()
        {ComparisonOperator.NotEquals, ComparisonOperator.Equals, ComparisonOperator.IsNull};

    public StringFilterType() : base(StringOperations)
    {
    }

    protected override string GetFilterValue(string value)
    {
        value = value.Trim('\'');
        return value.ToLowerInvariant().Equals("null") ? null : value;
    }
}