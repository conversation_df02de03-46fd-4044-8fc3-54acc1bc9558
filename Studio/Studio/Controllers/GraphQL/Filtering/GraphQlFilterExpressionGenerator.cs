using Studio.Common.Interfaces;
using Studio.Controllers.GraphQl.Filtering.Parser;
using System.Linq.Expressions;
using System.Reflection;

namespace Studio.Controllers.GraphQl.Filtering;

public class GraphQlFilterExpressionGenerator : IGraphQlFilterExpressionGenerator
{
    /// <inheritdoc />
    public Expression<Func<TModel, bool>> GenerateExpression<TModel>(string filterString)
        where TModel : class
    {
        if (string.IsNullOrWhiteSpace(filterString))
        {
            return null;
        }

        var filterModel = new GraphQlFilterParser().Parse(filterString);
        var modelParameterExpression = Expression.Parameter(typeof(TModel));

        Expression combinedExpression = null;
        LogicalNode logicalOperator = null;
        foreach (var node in filterModel.FilterExpressionNodes)
        {
            switch (node)
            {
                case null:
                    throw new ArgumentException($"Invalid Filter expression: Could not parse [{filterString}]");
                case FilterTermNode termNode:
                    {
                        var filterExpression = GetFilterTermExpression<TModel>(termNode, modelParameterExpression);

                        combinedExpression ??= filterExpression;

                        if (logicalOperator != null)
                        {
                            combinedExpression = logicalOperator.Operator switch
                            {
                                LogicalOperator.And => Expression.AndAlso(combinedExpression, filterExpression),
                                LogicalOperator.Or => Expression.OrElse(combinedExpression, filterExpression),
                                _ => throw new ArgumentOutOfRangeException(message: $"{nameof(logicalOperator.Operator)} has an unexpected value ({logicalOperator.Operator}) of type ({nameof(LogicalOperator)}", innerException: null)
                            };
                        }

                        break;
                    }
                case LogicalNode logicalNode:
                    logicalOperator = logicalNode;
                    break;
            }
        }

        return combinedExpression == null
            ? null
            : Expression.Lambda<Func<TModel, bool>>(combinedExpression, modelParameterExpression);
    }

    private static Expression GetFilterTermExpression<TModel>(
        FilterTermNode node,
        Expression modelParameterExpression
    )
    {
        var filterInfo = GetFilterInfo<TModel>(modelParameterExpression, node.PropertyName);
        return GetFilterTermExpression(node, filterInfo);
    }

    private static Expression GetFilterTermExpression(
        FilterTermNode expressionModelModel,
        FilterInfo filterInfo
    )
    {
        filterInfo.Operation = expressionModelModel.Operator;
        filterInfo.Value = expressionModelModel.Value;

        var filterType = FilterTypeFactory.GetFilterType(filterInfo.Property);
        return filterType.GetFilterExpression(filterInfo);
    }

    private static FilterInfo GetFilterInfo<TModel>(Expression parameterExpression, string propertyName)
    {
        var modelProperties =
            typeof(TModel).GetTypeInfo().GetProperties(BindingFlags.Instance | BindingFlags.Public);

        var currentPropertyExpression = Expression.Property(parameterExpression, propertyName);
        var currentProperty = modelProperties.FirstOrDefault(pi => string.Equals(pi.Name.ToLowerInvariant(),
            propertyName.ToLowerInvariant(),
            StringComparison.Ordinal));

        return new FilterInfo
        {
            AccessExpression = currentPropertyExpression,
            Property = currentProperty
        };
    }
}