using Studio.Controllers.GraphQl.Filtering.Tokenizers;
using Studio.Controllers.GraphQl.Filtering.Tokens;
using Studio.Domain.Exceptions;

namespace Studio.Controllers.GraphQl.Filtering.Parser;

internal class GraphQlFilterParser
{
    private readonly ITokenizer _tokenizer;
    private Stack<Token> _tokenSequence;
    private Token _lookaheadFirst;
    private Token _lookaheadSecond;

    internal GraphQlFilterParser()
    {
        _tokenizer = new RegexFilterTokenizer();
    }

    /// <summary>
    /// Generate an expression tree — a collection of nodes that represent the structure of the expression.
    /// Supported Operators in the expression [eq,ne,and,or]
    /// <para>An valid FilterExpression is composed by a FilterTerm and eventually a repetition of a LogicalOperator and a FilterTerm.
    /// A FilterTerm is composed by a Property and a Comparison followed by a Value.</para>
    /// EBNF gramma:
    /// <para>FilterExpression: = FilterTerm | FilterTerm LogicalOperator FilterTerm </para>
    /// <para>FilterTerm := Variable Comparison Value | FilterGroup</para>
    ///<para>FilterGroup := FilterTerm LogicalOperator FilterTerm</para>
    ///<para>Comparison := "eq", "ne"</para>
    ///<para>LogicalOperator  := "and", "or"</para>
    ///<para>Property := String</para>
    ///<para>Value := 'String' | null </para>
    /// </summary>
    /// <param name="filterString">The expression string</param>
    /// <returns></returns>
    internal FilterParserResult Parse(string filterString)
    {
        if (filterString == null)
        {
            throw new ArgumentNullException(nameof(filterString));
        }

        var tokens = _tokenizer.Tokenize(filterString.Trim()).ToList();

        LoadSequenceStack(tokens);
        PrepareLookAheadTokens();

        var expressionModel = ParseExpression();

        DiscardToken(TokenType.SequenceTerminator);

        return expressionModel;
    }

    private void LoadSequenceStack(IEnumerable<Token> tokens)
    {
        _tokenSequence = new Stack<Token>(tokens.Reverse());
    }

    private void PrepareLookAheadTokens()
    {
        _lookaheadFirst = _tokenSequence.Pop();
        _lookaheadSecond = _tokenSequence.Pop();
    }

    private FilterParserResult ParseExpression()
    {
        var filterExpression = new FilterParserResult();

        while (!IsSequenceTerminator(_lookaheadFirst))
        {
            if (IsFilterTerm())
            {
                filterExpression.FilterExpressionNodes.Add(GetFilterTerm());
            }
            else if (IsLogicOperator(_lookaheadFirst))
            {
                filterExpression.FilterExpressionNodes.Add(GetLogicNode());
                if (!IsFilterTerm())
                {
                    throw new GraphQlFilterParserException(
                        "Syntax error: Expected term but found" + _lookaheadFirst.Value);
                }

                filterExpression.FilterExpressionNodes.Add(GetFilterTerm());
            }
            else if (!IsSequenceTerminator(_lookaheadFirst))
            {
                throw new GraphQlFilterParserException("Syntax error: expected end of sequence but found" +
                                                       _lookaheadFirst.Value);
            }
        }

        return filterExpression;
    }

    private FilterTermNode GetFilterTerm()
    {
        return new FilterTermNode
        {
            PropertyName = GetProperty(_lookaheadFirst),
            Operator = GetComparisonOperator(_lookaheadFirst),
            Value = GetValue(_lookaheadFirst)
        };
    }

    private string GetValue(Token token)
    {
        if (!(token.TokenType.Equals(TokenType.StringValue) || token.TokenType.Equals(TokenType.Null)))
        {
            throw new GraphQlFilterParserException("Syntax error: Expected value token but found: " + token.Value);
        }

        var value = token.Value;
        DiscardToken();
        return value;
    }

    private LogicalNode GetLogicNode()
    {
        return new LogicalNode { Operator = GetLogicalOperator(_lookaheadFirst) };
    }

    private LogicalOperator GetLogicalOperator(Token token)
    {
        LogicalOperator value = token.TokenType switch
        {
            TokenType.And => LogicalOperator.And,
            TokenType.Or => LogicalOperator.Or,
            _ => throw new GraphQlFilterParserException("Expected and, or but found: " + token.Value)
        };

        DiscardToken();
        return value;
    }

    private ComparisonOperator GetComparisonOperator(Token token)
    {
        ComparisonOperator value = token.TokenType switch
        {
            TokenType.Equals => ComparisonOperator.Equals,
            TokenType.NotEquals => ComparisonOperator.NotEquals,
            _ => throw new GraphQlFilterParserException("Expected eq, ne but found: " + token.Value)
        };

        DiscardToken();
        return value;
    }

    private string GetProperty(Token token)
    {
        if (!token.TokenType.Equals(TokenType.Property))
        {
            throw new GraphQlFilterParserException("Syntax error: Expected property token but found: " +
                                                   token.Value);
        }

        var value = token.Value;
        DiscardToken();
        return value;
    }

    private void DiscardToken(TokenType tokenType)
    {
        if (_lookaheadFirst.TokenType != tokenType)
        {
            throw new GraphQlFilterParserException(
                $"Expected {tokenType.ToString().ToUpper()} but found: {_lookaheadFirst.Value}");
        }

        DiscardToken();
    }

    private void DiscardToken()
    {
        _lookaheadFirst = _lookaheadSecond.Clone();
        _lookaheadSecond = _tokenSequence.Any()
            ? _tokenSequence.Pop()
            : new Token(TokenType.SequenceTerminator, string.Empty);
    }

    private bool IsFilterTerm()
    {
        return IsProperty(_lookaheadFirst) && IsEqualityOperator(_lookaheadSecond);
    }

    private static bool IsLogicOperator(Token token)
    {
        return token.TokenType is TokenType.And or TokenType.Or;
    }

    private static bool IsProperty(Token token)
    {
        return token.TokenType.Equals(TokenType.Property);
    }

    private static bool IsEqualityOperator(Token token)
    {
        return token.TokenType == TokenType.Equals
               || token.TokenType == TokenType.NotEquals;
    }

    private static bool IsSequenceTerminator(Token token)
    {
        return token.TokenType == TokenType.SequenceTerminator;
    }
}