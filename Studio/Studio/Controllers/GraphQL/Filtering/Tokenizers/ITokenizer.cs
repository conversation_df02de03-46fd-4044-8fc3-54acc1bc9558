using Studio.Controllers.GraphQl.Filtering.Tokens;

namespace Studio.Controllers.GraphQl.Filtering.Tokenizers;

public interface ITokenizer
{
    /// <summary>
    ///  Break the filter expression string into a series of tokens where each token describes one piece of the filter expression.
    /// </summary>
    /// <param name="filterText"></param>
    /// <returns>Tokens</returns>
    IEnumerable<Token> Tokenize(string filterText); //Todo:Write tests
}