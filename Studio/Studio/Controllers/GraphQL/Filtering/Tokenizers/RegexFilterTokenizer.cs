using Studio.Controllers.GraphQl.Filtering.Tokens;
using System.Text.RegularExpressions;

namespace Studio.Controllers.GraphQl.Filtering.Tokenizers;

public class RegexFilterTokenizer : ITokenizer
{
    private readonly List<TokenDefinition> _tokenDefinitions;

    public RegexFilterTokenizer()
    {
        //order by priority
        _tokenDefinitions = new List<TokenDefinition>
        {
            new TokenDefinition(TokenType.And, "^and"),
            new TokenDefinition(TokenType.Or, "^or"),
            new TokenDefinition(TokenType.Null, "^null"),
            new TokenDefinition(TokenType.Equals, "^eq"),
            new TokenDefinition(TokenType.NotEquals, "^ne"),
            new TokenDefinition(TokenType.StringValue, "^'[^']+'"),
            new TokenDefinition(TokenType.Property, "^\\S*"),
        };
    }

    //todo: write tests
    public IEnumerable<Token> Tokenize(string filterText)
    {
        var tokens = new List<Token>();
        var remainingText = filterText;

        while (!string.IsNullOrWhiteSpace(remainingText))
        {
            var match = FindMatch(remainingText);
            if (match.IsMatch)
            {
                tokens.Add(new Token(match.TokenType, match.Value));
                remainingText = match.RemainingText;
            }
            else
            {
                if (IsWhitespace(remainingText))
                {
                    remainingText = remainingText.Substring(1);
                }
                else
                {
                    throw new Exception("Invalid token found: " + remainingText);
                }
            }
        }

        tokens.Add(new Token(TokenType.SequenceTerminator, string.Empty));

        return tokens;
    }

    private TokenMatch FindMatch(string filterText)
    {
        foreach (var tokenDef in _tokenDefinitions)
        {
            var match = tokenDef.Match(filterText);
            if (match.IsMatch)
            {
                return match;
            }
        }

        return new TokenMatch { IsMatch = false };
    }

    private static bool IsWhitespace(string lqlText)
    {
        return Regex.IsMatch(lqlText, "^\\s+");
    }
}