using GraphQL;
using GraphQL.Types;
using Studio.Application.Common.Interfaces;
using Studio.Application.Common.Interfaces.Services.BrandLibrary;
using Studio.Controllers.GraphQl.Extensions;
using Studio.Controllers.GraphQl.ViewModels.BrandLibrary;
using Studio.Domain;
using Studio.Domain.Entities;

namespace Studio.Controllers.GraphQl.Mutations;

public class BrandLibraryFolderMutation : IMutation
{
    private readonly IUserAuthorizationService _authorizationService;
    private readonly IBrandLibraryFolderService _folderService;
    private readonly IBrandLibraryService _brandLibraryService;

    public BrandLibraryFolderMutation(
        IUserAuthorizationService authorizationService,
        IBrandLibraryFolderService folderService,
        IBrandLibraryService brandLibraryService)
    {
        _authorizationService = authorizationService;
        _folderService = folderService;
        _brandLibraryService = brandLibraryService;
    }

    public void RegisterMutationsOn(StudioMutation studioMutation)
    {
        CreateFolder(studioMutation);
        RenameFolder(studioMutation);
        MoveContents(studioMutation);
        DeleteContents(studioMutation);
    }

    private void CreateFolder(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<BrandLibraryType>(GraphQlMutationName.CreateBrandLibraryFolder,
            "Creates a folder in the brand library",
            new QueryArguments(new List<QueryArgument>
            {
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.BrandLibraryId,
                    Description = "The brand library id"
                },
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.Name,
                    Description = "Folder name"
                },
                new QueryArgument<StringGraphType>
                {
                    Name = ArgumentName.ParentFolderId,
                    Description = "Parent folder id"
                }
            }),
            async context =>
            {
                var brandLibraryId = context.GetArgument<string>(ArgumentName.BrandLibraryId);
                var name = context.GetArgument<string>(ArgumentName.Name);
                var parentFolderId = context.GetArgument<string>(ArgumentName.ParentFolderId);

                var brandId = await _brandLibraryService.GetBrandId(brandLibraryId);

                var canAccess = await _authorizationService.HasBrandAccess(brandId);
                if (!canAccess)
                {
                    return context.Errors.AddForbiddenError();
                }

                var library = await _folderService.Create(brandLibraryId, name, parentFolderId);

                return !library.IsSuccess
                    ? context.Errors.AddError(library.ErrorMessage)
                    : library.Value;
            }
        );
    }

    private void RenameFolder(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<BrandLibraryType>(GraphQlMutationName.RenameBrandLibraryFolder,
            "Renames a folder in the brand library",
            new QueryArguments(new List<QueryArgument>
            {
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.BrandLibraryId,
                    Description = "The brand library id"
                },
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.Name,
                    Description = "New folder name"
                },
                new QueryArgument<StringGraphType>
                {
                    Name = ArgumentName.Id,
                    Description = "Folder id"
                }
            }),
            async context =>
            {
                var brandLibraryId = context.GetArgument<string>(ArgumentName.BrandLibraryId);
                var name = context.GetArgument<string>(ArgumentName.Name);
                var folderId = context.GetArgument<string>(ArgumentName.Id);

                var brandId = await _brandLibraryService.GetBrandId(brandLibraryId);

                var canAccess = await _authorizationService.HasBrandAccess(brandId);
                if (!canAccess)
                {
                    return context.Errors.AddForbiddenError();
                }

                var library = await _folderService.Rename(brandLibraryId, folderId, name);

                return !library.IsSuccess
                    ? context.Errors.AddError(library.ErrorMessage)
                    : library.Value;
            }
        );
    }

    private void MoveContents(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<BrandLibraryType>(GraphQlMutationName.MoveBrandLibraryContents,
            "Moves BL contents to or from folder",
            new QueryArguments(new List<QueryArgument>
            {
                new QueryArgument<NonNullGraphType<ListGraphType<StringGraphType>>>
                {
                    Name = ArgumentName.ContentIds,
                    Description = "BL contents to move"
                },
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.BrandLibraryId,
                    Description = "The brand library id"
                },
                new QueryArgument<StringGraphType>
                {
                    Name = ArgumentName.ParentFolderId,
                    Description = "Parent folder id (null when moving contents to root)"
                }
            }),
            async context =>
            {
                var contentIds = context.GetArgument<IReadOnlyList<string>>(ArgumentName.ContentIds);
                var brandLibraryId = context.GetArgument<string>(ArgumentName.BrandLibraryId);
                var parentFolderId = context.GetArgument<string>(ArgumentName.ParentFolderId);

                var brandId = await _brandLibraryService.GetBrandId(brandLibraryId);

                var canAccess = await _authorizationService.HasBrandAccess(brandId);
                if (!canAccess)
                {
                    return context.Errors.AddForbiddenError();
                }

                var library = await _folderService.MoveContents(contentIds, brandLibraryId, parentFolderId);

                return !library.IsSuccess
                    ? context.Errors.AddError(library.ErrorMessage)
                    : library.Value;
            }
        );
    }

    private void DeleteContents(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<BrandLibraryType>(GraphQlMutationName.DeleteBrandLibraryContents,
            "Deletes BL contents",
            new QueryArguments(new List<QueryArgument>
            {
                new QueryArgument<NonNullGraphType<ListGraphType<StringGraphType>>>
                {
                    Name = ArgumentName.ContentIds,
                    Description = "BL contents to delete"
                },
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.BrandLibraryId,
                    Description = "The brand library id"
                }
            }),
            async context =>
            {
                var contentIds = context.GetArgument<IReadOnlyList<string>>(ArgumentName.ContentIds);
                var brandLibraryId = context.GetArgument<string>(ArgumentName.BrandLibraryId);

                var brandId = await _brandLibraryService.GetBrandId(brandLibraryId);

                var canAccess = await _authorizationService.HasBrandAccess(brandId);
                if (!canAccess)
                {
                    return context.Errors.AddForbiddenError();
                }

                var library = await _folderService.DeleteContents(contentIds, brandLibraryId);

                return !library.IsSuccess
                    ? context.Errors.AddError(library.ErrorMessage)
                    : library.Value;
            }
        );
    }
}