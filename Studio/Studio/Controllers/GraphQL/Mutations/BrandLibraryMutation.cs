using GraphQL;
using GraphQL.Types;
using Studio.Application.Common.Interfaces;
using Studio.Application.Common.Interfaces.Services.BrandLibrary;
using Studio.Application.Dtos;
using Studio.Controllers.GraphQl.Extensions;
using Studio.Controllers.GraphQl.ViewModels.BrandLibrary;
using Studio.Controllers.GraphQl.ViewModels.Elements;
using Studio.Domain;
using Studio.Domain.Exceptions;

namespace Studio.Controllers.GraphQl.Mutations;

public class BrandLibraryMutation : IMutation
{
    private readonly IUserAuthorizationService _authorizationService;
    private readonly IBrandLibraryService _brandLibraryService;

    public BrandLibraryMutation(
        IUserAuthorizationService authorizationService,
        IBrandLibraryService brandLibraryService
    )
    {
        _authorizationService = authorizationService;
        _brandLibraryService = brandLibraryService;
    }

    public void RegisterMutationsOn(StudioMutation studioMutation)
    {
        CreateElementInBrandLibrary(studioMutation);
        UpdateElementInBrandLibrary(studioMutation);
        DeleteElementsInBrandLibrary(studioMutation);
    }

    private void CreateElementInBrandLibrary(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<BrandLibraryType>(GraphQlMutationName.CreateElementInBrandLibrary,
            "Creates an element in the brand library",
            new QueryArguments(new List<QueryArgument>
            {
                new QueryArgument<NonNullGraphType<CreateElementInputType>>
                {
                    Name = ArgumentName.Element,
                    Description = "The element to add to the brand library",
                },
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.BrandLibraryId,
                    Description = "The brand library id"
                },
                new QueryArgument<GuidGraphType>
                {
                    Name = ArgumentName.OriginalElementId,
                    Description = "The id of the element used to create the brand library element"
                }
            }),
            async context =>
            {
                var brandLibraryId = context.GetArgument<string>(ArgumentName.BrandLibraryId);
                var originalElementId = context.GetArgument<Guid>(ArgumentName.OriginalElementId);
                var element = context.GetArgument<ElementDto>(ArgumentName.Element);
                var brandId = await _brandLibraryService.GetBrandId(brandLibraryId);

                var canAccess = await _authorizationService.HasBrandAccess(brandId);
                if (!canAccess)
                {
                    return context.Errors.AddForbiddenError();
                }

                try
                {
                    var createBrandLibraryElementDto =
                        new CreateBrandLibraryElementDto(brandLibraryId, element, originalElementId);

                    var library = await _brandLibraryService.CreateElement(createBrandLibraryElementDto);

                    return library;
                }
                catch(ValidationException e)
                {
                    return context.Errors.AddBadRequestError(e.Message);
                }
            }
        );
    }

    private void DeleteElementsInBrandLibrary(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<BrandLibraryType>(GraphQlMutationName.DeleteElementsInBrandLibrary,
            "Deletes an element in the brand library",
            new QueryArguments(new List<QueryArgument>
            {
                new QueryArgument<ListGraphType<StringGraphType>>
                {
                    Name = ArgumentName.Ids,
                    Description = "The ids of the elements to delete from the brand library",
                },
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.BrandLibraryId,
                    Description = "The brand library id"
                }
            }),
            async context =>
            {
                var brandLibraryId = context.GetArgument<string>(ArgumentName.BrandLibraryId);
                var brandId = await _brandLibraryService.GetBrandId(brandLibraryId);

                var canAccess = await _authorizationService.HasBrandAccess(brandId);
                if (!canAccess)
                {
                    return context.Errors.AddForbiddenError();
                }

                var elementIds = context.GetArgument<string[]>(ArgumentName.Ids);
                var library = await _brandLibraryService.DeleteElements(elementIds, brandLibraryId);

                return !library.IsSuccess
                    ? context.Errors.AddError(library.ErrorMessage)
                    : library.Value;
            }
        );
    }

    private void UpdateElementInBrandLibrary(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<BrandLibraryType>(GraphQlMutationName.UpdateElementInBrandLibrary,
            "Updates an element in the brand library",
            new QueryArguments(new List<QueryArgument>
            {
                new QueryArgument<NonNullGraphType<ElementInputType>>
                {
                    Name = ArgumentName.Element,
                    Description = "The element to update in the brand library"
                },
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.BrandLibraryId,
                    Description = "The brand library id"
                }
            }),
            async context =>
            {
                if (!int.TryParse(context.GetArgument<string>(ArgumentName.BrandLibraryId), out var brandLibraryId))
                {
                    return context.Errors.AddBadRequestError($"{ArgumentName.BrandLibraryId} was not a valid integer");
                }

                var brandId = await _brandLibraryService.GetBrandId(brandLibraryId);
                var canAccess = await _authorizationService.HasBrandAccess(brandId);
                if (!canAccess)
                {
                    return context.Errors.AddForbiddenError();
                }

                var element = context.GetArgument<ElementDto>(ArgumentName.Element);
                try
                {
                    return await _brandLibraryService.UpdateElement(element, brandLibraryId);
                }
                catch (ValidationException e)
                {
                    return context.Errors.AddBadRequestError(e.Message);
                }
            }
        );
    }
}