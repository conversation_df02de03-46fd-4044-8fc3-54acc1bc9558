using GraphQL;
using GraphQL.Types;
using Studio.Application.Common.Interfaces;
using Studio.Application.Common.Interfaces.Services.CreativeGeneration;
using Studio.Application.Common.Interfaces.Services.CreativeSet;
using Studio.Application.Dtos.CreativeGeneration;
using Studio.Common.Constants;
using Studio.Controllers.GraphQl.Extensions;
using Studio.Controllers.GraphQl.ViewModels.CreativeOptimization;
using Studio.Domain;
using Studio.Domain.Entities;
using Studio.Dtos;

namespace Studio.Controllers.GraphQl.Mutations;

public class CreativeGenerationMutation : IMutation
{
    private readonly ICreativeGenerationService _creativeGenerationService;
    private readonly ICreativeSetBrandService _creativeSetBrandService;
    private readonly IUserAuthorizationService _authorizationService;
    private readonly ILogger<CreativeGenerationMutation> _logger;

    public CreativeGenerationMutation(
        ICreativeGenerationService creativeGenerationService,
        ICreativeSetBrandService creativeSetBrandService,
        IUserAuthorizationService authorizationService,
        ILogger<CreativeGenerationMutation> logger)
    {
        _creativeGenerationService = creativeGenerationService;
        _creativeSetBrandService = creativeSetBrandService;
        _authorizationService = authorizationService;
        _logger = logger;
    }

    public void RegisterMutationsOn(StudioMutation studioMutation)
    {
        BeginCreativeGenerationMutation(studioMutation);
    }

    private void BeginCreativeGenerationMutation(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<CreativeGenerationResponseType>(GraphQlMutationName.BeginCreativeGeneration,
            "Begins the creative generation process.",
            new QueryArguments(new List<QueryArgument>
            {
                new QueryArgument<NonNullGraphType<ListGraphType<CreativesetCreativeIdsInputType>>>
                {
                    Name = ArgumentName.CreativesToGenerate,
                    Description = "The creatives to be generated",
                },
                new QueryArgument<StringGraphType>
                {
                    Name = ArgumentName.Requester,
                    Description = "The requester of the creative generation. Default is CampaignService",
                    DefaultValue = Requester.CampaignService.ToString()
                }
            }),
            async context =>
            {
                var creativesToGenerateArgument =
                    context.GetArgument<List<CreativeSetCreativeIds>>(ArgumentName.CreativesToGenerate);

                var requester = context.GetArgument<string>(ArgumentName.Requester);
                var requesterEnum = Enum.TryParse(requester, out Requester r) ? r : Requester.CampaignService;

                if (!creativesToGenerateArgument.Any())
                {
                    return context.Errors.AddError(ErrorMessages.MustBeProvided("creativesToGenerate"));
                }

                var creativesToGenerate = new Dictionary<int, List<int>>();

                foreach (var creative in creativesToGenerateArgument)
                {
                    var creativeSetId = int.Parse(creative.CreativeSetId);
                    var creatives = creative.CreativeIds.Select(int.Parse).ToList();

                    if (creativesToGenerate.ContainsKey(creativeSetId))
                    {
                        var existingCreatives = creativesToGenerate[creativeSetId];
                        creativesToGenerate[creativeSetId] = existingCreatives.Union(creatives).ToList();
                    }
                    else
                    {
                        creativesToGenerate.Add(creativeSetId, creatives);
                    }
                }

                var creativeSetIds = creativesToGenerate.Select(c => c.Key).ToList();

                using var _ = _logger.BeginScope(new Dictionary<string, object>
                {
                    { "creativeSetIds", creativeSetIds },
                    { "creativeIds", creativesToGenerate.SelectMany(c => c.Value) },
                });

                var brandIds = await _creativeSetBrandService.GetBrandIds(creativeSetIds);
                if (!brandIds.IsSuccess || !brandIds.Value.Any())
                {
                    return context.Errors.AddError(ErrorMessages.NotFound(nameof(CreativesetEntity)));
                }

                if (brandIds.Value.Distinct().Count() > 1)
                {
                    return context.Errors.AddError(ErrorMessages.AllCreativeSetsMustBelongToSameBrand);
                }

                var brandId = brandIds.Value.First();
                var hasBrandAccess = await _authorizationService.HasBrandAccess(brandId);
                if (!hasBrandAccess)
                {
                    foreach (var creativeSetId in creativeSetIds)
                    {
                        if (!await _authorizationService.HasOperationAccess(creativeSetId.ToString(), context.FieldName))
                        {
                            return context.Errors.AddForbiddenError();
                        }
                    }
                }

                var result = await _creativeGenerationService.InitCreativeGeneration(
                    creativesToGenerate,
                    brandId,
                    requesterEnum,
                    CreativeGenerationDto.TriggerType.Generation,
                    context.CancellationToken);

                return result.IsSuccess
                    ? result.Value
                    : context.Errors.AddError(result.ErrorMessage);
            });
    }
}