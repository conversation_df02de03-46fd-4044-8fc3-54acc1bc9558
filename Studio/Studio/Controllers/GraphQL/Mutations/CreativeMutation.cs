using GraphQL;
using GraphQL.Types;
using MassTransit;
using Studio.Application.Common.Interfaces;
using Studio.Application.Common.Interfaces.Services.Creative;
using Studio.Application.Common.Interfaces.Services.CreativeSet;
using Studio.Application.Dtos.Creative;
using Studio.Common.Constants;
using Studio.Contracts.Events;
using Studio.Controllers.GraphQl.Extensions;
using Studio.Controllers.GraphQl.ViewModels.Creatives;
using Studio.Domain;
using Studio.Domain.Entities;

namespace Studio.Controllers.GraphQl.Mutations;

public class CreativeMutation : IMutation
{
    private readonly ICreativeSetBrandService _creativeSetBrandService;
    private readonly IUserAuthorizationService _authorizationService;
    private readonly IUpdateCreativeService _updateCreativeService;
    private readonly ISetApprovalStatusOnCreativesService _setApprovalStatusService;
    private readonly IBus _bus;
    private readonly IUserProvider _userProvider;

    public CreativeMutation(
        ICreativeSetBrandService creativeSetBrandService,
        IUserAuthorizationService authorizationService,
        IUpdateCreativeService updateCreativeService,
        ISetApprovalStatusOnCreativesService setApprovalStatusService,
        IBus bus,
        IUserProvider userProvider
    )
    {
        _creativeSetBrandService = creativeSetBrandService;
        _authorizationService = authorizationService;
        _updateCreativeService = updateCreativeService;
        _setApprovalStatusService = setApprovalStatusService;
        _bus = bus;
        _userProvider = userProvider;
    }

    public void RegisterMutationsOn(StudioMutation studioMutation)
    {
        UpdateCreatives(studioMutation);
        SetApprovalStatusOnCreatives(studioMutation);
    }

    private void UpdateCreatives(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<ListGraphType<UpdateCreativeType>>(GraphQlMutationName.UpdateCreatives,
            "Updates a creatives",
            new QueryArguments(new List<QueryArgument>
            {
                new QueryArgument<ListGraphType<UpdateCreativeInputType>>
                {
                    Name = ArgumentName.Creatives,
                    Description = "The creatives to update"
                }
            }),
            async context =>
            {
                var updateCreatives = context.GetArgument<UpdateCreativeDto[]>(ArgumentName.Creatives);

                var creativeIds = updateCreatives
                    .Select(c => int.TryParse(c.Id, out var creativeId) ? creativeId : default)
                    .Where(id => id != default)
                    .ToHashSet();

                var associatedBrandIdsResult = await _updateCreativeService.GetBrandIds(creativeIds);
                if (!associatedBrandIdsResult.IsSuccess)
                {
                    return context.Errors.AddError(associatedBrandIdsResult.ErrorMessage);
                }

                var uniqueBrandIds = associatedBrandIdsResult.Value.Distinct().ToList();
                if (!uniqueBrandIds.Any())
                {
                    return context.Errors.AddNotFoundError(ErrorMessages.NotFound("creatives"));
                }

                if (uniqueBrandIds.Count > 1)
                {
                    return context.Errors.AddError(ErrorMessages.AllCreativesMustBelongToSameBrand);
                }

                var canAccess =
                    await _authorizationService.HasBrandAccess(uniqueBrandIds.Single());
                if (!canAccess)
                {
                    return context.Errors.AddForbiddenError();
                }

                var updateResult = await _updateCreativeService.UpdateCreatives(updateCreatives, context.CancellationToken);

                if (updateResult.IsSuccess)
                {
                    var creativeSetId = int.Parse(updateResult.Value.First().CreativeSetId);
                    var userId = _userProvider.GetUser()?.UserId;
                    await _bus.Publish(new NotifyCreativesetChangedEvent(creativeSetId, userId));
                }

                return !updateResult.IsSuccess
                    ? context.Errors.AddError(updateResult.ErrorMessage)
                    : updateResult.Value;
            });
    }

    private void SetApprovalStatusOnCreatives(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<ListGraphType<UpdateCreativeType>>(GraphQlMutationName.SetApprovalStatusOnCreatives,
            "Sets approval status on multiple creatives",
            new QueryArguments(new List<QueryArgument>
            {
                new QueryArgument<NonNullGraphType<ListGraphType<UpdateCreativeInputType>>>
                {
                    Name = ArgumentName.Creatives,
                    Description = "The creatives to update"
                },
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.CreativeSetId,
                    Description = "The creativeSet ID"
                }
            }),
            async context =>
            {
                var updateCreatives = context.GetArgument<List<UpdateCreativeDto>>(ArgumentName.Creatives);
                var creativeSetId = context.GetArgument<string>(ArgumentName.CreativeSetId);

                // TODO: Let FE send brand id instead
                var brandIdResult = await _creativeSetBrandService.GetBrandId(creativeSetId);
                if (!brandIdResult.IsSuccess)
                {
                    return context.Errors.AddError(brandIdResult.ErrorMessage);
                }

                if (brandIdResult.Value == null)
                {
                    return context.Errors.AddNotFoundError<CreativesetEntity>();
                }

                var authorized =
                    await _authorizationService.HasAccess(brandIdResult.Value, creativeSetId, context.FieldName);
                if (!authorized)
                {
                    return context.Errors.AddForbiddenError();
                }

                var updateResult = await _setApprovalStatusService.SetApprovalStatus(updateCreatives);

                return !updateResult.IsSuccess
                    ? context.Errors.AddError(updateResult.ErrorMessage)
                    : updateResult.Value;
            });
    }
}