using GraphQL;
using GraphQL.Types;
using Studio.Application.Common.Interfaces;
using Studio.Application.Common.Interfaces.Services.CreativeSet;
using Studio.Application.Dtos.Creative;
using Studio.Controllers.GraphQl.Extensions;
using Studio.Controllers.GraphQl.ViewModels.Creatives;
using Studio.Controllers.GraphQl.ViewModels.CreativeSet;
using Studio.Domain;
using Studio.Domain.Entities;

namespace Studio.Controllers.GraphQl.Mutations;

public class CreativeSetMutation : IMutation
{
    private readonly IUserProvider _userProvider;
    private readonly ICreativeSetBrandService _creativeSetBrandService;
    private readonly IAuthorizationService _authorizationService;
    private readonly IUpdateCreativeSetStateService _updateCreativeSetStateService;

    public CreativeSetMutation(
        IUserProvider userProvider,
        ICreativeSetBrandService creativeSetBrandService,
        IAuthorizationService authorizationService,
        IUpdateCreativeSetStateService updateCreativeSetStateService
    )
    {
        _userProvider = userProvider;
        _creativeSetBrandService = creativeSetBrandService;
        _authorizationService = authorizationService;
        _updateCreativeSetStateService = updateCreativeSetStateService;
    }

    public void RegisterMutationsOn(StudioMutation studioMutation)
    {
        UpdateCreativeSetStateId(studioMutation);
    }

    private void UpdateCreativeSetStateId(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<CreativesetResponseType>(GraphQlMutationName.UpdateCreativeSetStateId,
            "Updated state id on the creativeSet and checksums of the affected creatives",
            new QueryArguments(new List<QueryArgument>
            {
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.CreativeSetId
                },
                new QueryArgument<StringGraphType>
                {
                    Name = ArgumentName.StateId
                },
                new QueryArgument<NonNullGraphType<ListGraphType<UpdateCreativeInputType>>>
                {
                    Name = ArgumentName.Creatives
                }
            }),
            async context =>
            {
                var creativeSetId = context.GetArgument<string>(ArgumentName.CreativeSetId);
                var stateId = context.GetArgument<string>(ArgumentName.StateId);
                var creativesToUpdate = context.GetArgument<UpdateCreativeDto[]>(ArgumentName.Creatives);

                var brandIdResult = await _creativeSetBrandService.GetBrandId(creativeSetId);
                if (!brandIdResult.IsSuccess)
                {
                    return context.Errors.AddNotFoundError<CreativesetEntity>();
                }

                var user = _userProvider.GetUser();
                var canAccess = await _authorizationService.HasBrandAccess(user, brandIdResult.Value);
                if (!canAccess)
                {
                    return context.Errors.AddForbiddenError();
                }

                var creativeSet =
                    await _updateCreativeSetStateService.UpdateStateId(
                        creativeSetId,
                        stateId,
                        creativesToUpdate,
                        brandIdResult.Value,
                        user.UserId,
                        context.CancellationToken);

                return creativeSet.IsSuccess
                    ? creativeSet.Value
                    : context.Errors.AddError(creativeSet.ErrorMessage);
            });
    }
}