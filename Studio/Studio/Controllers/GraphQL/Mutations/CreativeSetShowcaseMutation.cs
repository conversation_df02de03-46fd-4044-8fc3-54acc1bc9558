using GraphQL;
using GraphQL.Types;
using Studio.Application.Common.Interfaces;
using Studio.Application.Common.Interfaces.Services.CreativeSet;
using Studio.Application.Dtos.CreativeSetShowcase;
using Studio.Common.Constants;
using Studio.Controllers.GraphQl.Extensions;
using Studio.Controllers.GraphQl.ViewModels.CreativeSetShowcase;
using Studio.Domain;
using Studio.Domain.Entities;
using Studio.Domain.Enums;

namespace Studio.Controllers.GraphQl.Mutations;

public class CreativeSetShowcaseMutation : IMutation
{
    private readonly ICreativeSetShowcaseService _showcaseService;
    private readonly IUserAuthorizationService _authorizationService;
    private readonly ICreativeSetBrandService _creativeSetBrandService;
    private readonly IUserProvider _userProvider;

    public CreativeSetShowcaseMutation(
        ICreativeSetShowcaseService showcaseService,
        ICreativeSetBrandService creativeSetBrandService,
        IUserAuthorizationService authorizationService,
        IUserProvider userProvider)
    {
        _showcaseService = showcaseService;
        _creativeSetBrandService = creativeSetBrandService;
        _authorizationService = authorizationService;
        _userProvider = userProvider;
    }

    public void RegisterMutationsOn(StudioMutation studioMutation)
    {
        CreateShowcase(studioMutation);
        UpdateShowcase(studioMutation);
        DeleteShowcase(studioMutation);
    }

    private void CreateShowcase(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<CreativesetShowcaseType>(
            GraphQlMutationName.CreateCreativeSetShowcase,
            "Creates a creativeSet showcase",
            new QueryArguments(
                new QueryArgument<NonNullGraphType<CreativesetShowcaseInputType>>
                {
                    Name = ArgumentName.CreativeSetShowcase,
                    Description = "CreativeSet showcase"
                }),
            async context =>
            {
                var showcase = context.GetArgument<CreativeSetShowcaseInputDto>(ArgumentName.CreativeSetShowcase);
                if (!await IsRequestValid(showcase, context)) return null;

                var user = _userProvider.GetUser();
                if (user == null)
                {
                    return context.Errors.AddForbiddenError();
                }

                var showcaseResult = await _showcaseService.Create(showcase, user);
                if (!showcaseResult.IsSuccess)
                {
                    return context.Errors.AddError(showcaseResult.ErrorMessage);
                }

                return showcaseResult.Value;
            });
    }

    private void UpdateShowcase(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<CreativesetShowcaseType>(
            GraphQlMutationName.UpdateCreativeSetShowcase,
            "Updates a creativeSet showcase",
            new QueryArguments(
                new QueryArgument<NonNullGraphType<CreativesetShowcaseInputType>>
                {
                    Name = ArgumentName.CreativeSetShowcase,
                    Description = "CreativeSet showcase"
                }),
            async context =>
            {
                var showcase = context.GetArgument<CreativeSetShowcaseInputDto>(ArgumentName.CreativeSetShowcase);
                if (!await IsRequestValid(showcase, context)) return null;

                var showcaseResult = await _showcaseService.Update(showcase, context.CancellationToken);

                return showcaseResult;
            });
    }

    private void DeleteShowcase(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<BooleanGraphType>(
            GraphQlMutationName.DeleteCreativeSetShowcase,
            "Deletes a creativeSet showcase",
            new QueryArguments(
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.ShowcaseKey,
                    Description = "Showcase key"
                }),
            async context =>
            {
                var showcaseKey = context.GetArgument<string>(ArgumentName.ShowcaseKey);

                var deleteResult = await _showcaseService.Delete(showcaseKey);
                if (!deleteResult.IsSuccess)
                {
                    return context.Errors.AddError(deleteResult.ErrorMessage);
                }

                return deleteResult.Value;
            });
    }

    private async Task<bool> IsRequestValid(
        CreativeSetShowcaseInputDto showcase,
        IResolveFieldContext<object> context
    )
    {
        if (string.IsNullOrWhiteSpace(showcase.CreativesetId))
        {
            context.Errors.AddError(ErrorMessages.MustBeProvided(nameof(showcase.CreativesetId)));
            return false;
        }

        if (showcase.AllowedOperations is not null && showcase.AllowedOperations.Any() &&
            !showcase.AllowedOperations.All(operation => Enum.TryParse(typeof(AllowedOperations), operation, true, out _)))
        {
            context.Errors.AddError(ErrorMessages.MustBeProvided(nameof(showcase.AllowedOperations)));
            return false;
        }

        var brandId = await _creativeSetBrandService.GetBrandId(showcase.CreativesetId);
        if (!brandId.IsSuccess)
        {
            context.Errors.AddError(ErrorMessages.NotFound(nameof(CreativesetEntity)));
            return false;
        }

        if (brandId.Value == null)
        {
            context.Errors.AddNotFoundError<CreativesetEntity>();
            return false;
        }

        var canAccess = await _authorizationService.HasAccess(brandId.Value, showcase.CreativesetId, context.FieldName);
        if (!canAccess)
        {
            context.Errors.AddForbiddenError();
            return false;
        }

        return true;
    }
}