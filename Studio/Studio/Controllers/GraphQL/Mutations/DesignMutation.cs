using GraphQL;
using GraphQL.Types;
using MediatR;
using Studio.Application.Common.Interfaces.Services.Creative;
using Studio.Application.Designs.Commands.DeleteDesigns;
using Studio.Controllers.GraphQl.Extensions;
using Studio.Controllers.GraphQl.ViewModels.Common;
using Studio.Domain;

namespace Studio.Controllers.GraphQl.Mutations;

public class DesignMutation : IMutation
{
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly IGetCreativeService _creativeService;

    public DesignMutation(IServiceScopeFactory serviceScopeFactory, IGetCreativeService creativeService)
    {
        _serviceScopeFactory = serviceScopeFactory;
        _creativeService = creativeService;
    }

    public void RegisterMutationsOn(StudioMutation studioMutation)
    {
        DeleteDesigns(studioMutation);
    }

    private void DeleteDesigns(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<DeleteDesignsResponseType>(
            GraphQlMutationName.DeleteDesigns,
            "Deletes multiple designs in the same creativeSet. Will also remove all associated sizes and creatives.",
            new QueryArguments(
                new List<QueryArgument>
                {
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = ArgumentName.CreativeSetId,
                        Description = "ID of the creativeSet containing the designs to delete"
                    },
                    new QueryArgument<NonNullGraphType<ListGraphType<StringGraphType>>>
                    {
                        Name = ArgumentName.DesignIds,
                        Description = "IDs of the designs to delete"
                    }
                }),
            async context =>
            {
                var creativeSetId = context.GetArgument<string>(ArgumentName.CreativeSetId);
                var designIds = context.GetArgument<List<string>>(ArgumentName.DesignIds);

                // Can't directly inject IMediator because this class is registered as a singleton
                // and the IStudioContext that is injected in the handler is registered as scoped.
                var scope = _serviceScopeFactory.CreateScope();
                var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
                var deleteResult = await mediator.Send(new DeleteDesignsCommand(creativeSetId, designIds));

                if (!deleteResult.IsSuccess)
                {
                    return context.Errors.AddError(deleteResult);
                }

                return deleteResult.Value;
            });

        studioMutation.FieldAsync<DeleteDesignsResponseType>(
            GraphQlMutationName.DeleteDesignUsingSizeId,
            "Deletes design in the same creativeSet. Will also remove all associated sizes and creatives.",
            new QueryArguments(
                new List<QueryArgument>
                {
                    new QueryArgument<NonNullGraphType<StringGraphType>>
                    {
                        Name = ArgumentName.CreativeSetId,
                        Description = "ID of the creativeSet containing the designs to delete"
                    },
                    new QueryArgument<NonNullGraphType<ListGraphType<StringGraphType>>>
                    {
                        Name = ArgumentName.SizeId,
                        Description = "ID of the size to delete the design"
                    }
                }),
            async context =>
            {
                var creativeSetId = context.GetArgument<string>(ArgumentName.CreativeSetId);
                var sizeId = context.GetArgument<string>(ArgumentName.SizeId);

                var desingId = await _creativeService.GetDesignIdBySizeId(int.Parse(sizeId));

                // Can't directly inject IMediator because this class is registered as a singleton
                // and the IStudioContext that is injected in the handler is registered as scoped.
                var scope = _serviceScopeFactory.CreateScope();
                var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
                var deleteResult = await mediator.Send(new DeleteDesignsCommand(creativeSetId, [desingId.ToString()]));

                if (!deleteResult.IsSuccess)
                {
                    return context.Errors.AddError(deleteResult);
                }

                return deleteResult.Value;
            });
    }
}