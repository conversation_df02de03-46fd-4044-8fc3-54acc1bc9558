using GraphQL;
using GraphQL.Types;
using Studio.Application.Common.Interfaces;
using Studio.Application.Common.Interfaces.Services.ExportCreative;
using Studio.Application.Dtos;
using Studio.Common.Constants;
using Studio.Controllers.GraphQl.Extensions;
using Studio.Controllers.GraphQl.ViewModels.ExportCreative;
using Studio.Domain;

namespace Studio.Controllers.GraphQl.Mutations;

public class ExportCreativeRequestMutation : IMutation
{
    private readonly IExportCreativeRequestService _exportCreativeRequestService;
    private readonly IAuthorizationService _authorizationService;
    private readonly IUserProvider _userProvider;

    public ExportCreativeRequestMutation(
        IExportCreativeRequestService exportCreativeRequestService,
        IAuthorizationService authorizationService,
        IUserProvider userProvider
    )
    {
        _exportCreativeRequestService = exportCreativeRequestService;
        _authorizationService = authorizationService;
        _userProvider = userProvider;
    }

    public void RegisterMutationsOn(StudioMutation studioMutation)
    {
        CreateExportCreativeRequest(studioMutation);
    }

    private void CreateExportCreativeRequest(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<ExportCreativeRequestResponseType>(GraphQlMutationName.CreateExportCreativeRequest,
            "",
            new QueryArguments(new List<QueryArgument>
            {
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.BrandId,
                    Description = "The brandId"
                },
                new QueryArgument<NonNullGraphType<ExportCreativeRequestInputType>>
                {
                    Name = ArgumentName.ExportCreativeRequest,
                    Description = "The export request and settings"
                }
            }),
            async context =>
            {
                var brandId = context.GetArgument<string>(ArgumentName.BrandId);
                var exportCreativeRequest =
                    context.GetArgument<ExportCreativeRequestDto>(ArgumentName.ExportCreativeRequest);

                if (string.IsNullOrWhiteSpace(brandId))
                {
                    return context.Errors.AddError(ErrorMessages.MustBeProvided(nameof(brandId)));
                }

                var user = _userProvider.GetUser();
                if (user == null)
                {
                    return context.Errors.AddForbiddenError();
                }

                var canAccess =
                    await _authorizationService.HasBrandAccess(user, brandId);
                if (!canAccess)
                {
                    return context.Errors.AddForbiddenError();
                }

                var exportCreativeRequestResult =
                    await _exportCreativeRequestService.Create(exportCreativeRequest, user.UserId, context.CancellationToken);
                return !exportCreativeRequestResult.IsSuccess
                    ? context.Errors.AddError(exportCreativeRequestResult.ErrorMessage)
                    : exportCreativeRequestResult.Value;
            });
    }
}