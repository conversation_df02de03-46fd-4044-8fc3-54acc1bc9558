using GraphQL;
using GraphQL.Types;
using Studio.Application.Common.Interfaces;
using Studio.Application.Common.Interfaces.Services.CreativeSet;
using Studio.Application.Common.Interfaces.Services.Size;
using Studio.Application.Dtos;
using Studio.Common.Constants;
using Studio.Common.Validations;
using Studio.Controllers.GraphQl.Extensions;
using Studio.Controllers.GraphQl.ViewModels.CreativeSet;
using Studio.Controllers.GraphQl.ViewModels.Size;
using Studio.Domain;
using Studio.Domain.Entities;

namespace Studio.Controllers.GraphQl.Mutations;

// TODO do not return full creativeSet on these mutations, do as we have done for designs and versions
public class SizeMutation : IMutation
{
    private readonly IUserProvider _userProvider;
    private readonly IUserAuthorizationService _authorizationService;
    private readonly ICreateSizesService _createSizesService;
    private readonly IRenameSizesService _renameSizeService;
    private readonly IDeleteSizesService _deleteSizesService;
    private readonly ICreativeSetBrandService _creativeSetBrandService;
    private readonly ICreativeSetSnapshotService _creativeSetSnapshotService;
    private readonly IGetCreativeSetService _getCreativeSetService;

    public SizeMutation(
        IUserProvider userProvider,
        IUserAuthorizationService authorizationService,
        ICreateSizesService createSizesService,
        IRenameSizesService renameSizeService,
        IDeleteSizesService deleteSizesService,
        ICreativeSetBrandService creativeSetBrandService,
        ICreativeSetSnapshotService creativeSetSnapshotService,
        IGetCreativeSetService getCreativeSetService
    )
    {
        _authorizationService = authorizationService;
        _userProvider = userProvider;
        _createSizesService = createSizesService;
        _renameSizeService = renameSizeService;
        _deleteSizesService = deleteSizesService;
        _creativeSetBrandService = creativeSetBrandService;
        _creativeSetSnapshotService = creativeSetSnapshotService;
        _getCreativeSetService = getCreativeSetService;
    }

    public void RegisterMutationsOn(StudioMutation studioMutation)
    {
        RenameSizeInCreativeSet(studioMutation);
        CreateSizesInCreativeSet(studioMutation);
        DeleteSizesInCreativeSet(studioMutation);
    }

    private void RenameSizeInCreativeSet(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<CreativesetResponseType>(GraphQlMutationName.RenameSizeInCreativeSet,
            "Gives the size a custom name or renames a previously named size",
            new QueryArguments(new List<QueryArgument>
            {
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.CreativeSetId,
                    Description = "Id of the creativeSet where in the size to rename belongs"
                },
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.SizeId,
                    Description = "Id of size whose name to change"
                },
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.Name,
                    Description = "Name to give to the size"
                }
            }),
            async context =>
            {
                var creativeSetId = context.GetArgument<string>(ArgumentName.CreativeSetId);
                var sizeId = context.GetArgument<string>(ArgumentName.SizeId);
                var name = context.GetArgument<string>(ArgumentName.Name);

                if (string.IsNullOrWhiteSpace(creativeSetId))
                {
                    return context.Errors.AddError(ErrorMessages.MustBeProvided(nameof(creativeSetId)));
                }

                var brandIdResult = await _creativeSetBrandService.GetBrandId(creativeSetId);
                if (!brandIdResult.IsSuccess)
                {
                    return context.Errors.AddNotFoundError<CreativesetEntity>();
                }

                var authorized =
                    await _authorizationService.HasAccess(brandIdResult.Value, creativeSetId, context.FieldName);
                if (!authorized)
                {
                    return context.Errors.AddForbiddenError();
                }

                var renameResult = await _renameSizeService.Rename(sizeId, name.Trim(), creativeSetId);
                if (!renameResult.IsSuccess)
                {
                    return context.Errors.AddError(renameResult.ErrorMessage);
                }

                var creativeSet = await _getCreativeSetService.GetCreativeSetIncludingAllRelations(creativeSetId);
                if (!creativeSet.IsSuccess)
                {
                    return context.Errors.AddError(creativeSet.ErrorMessage);
                }

                //need to take snapshot for export and filename resolver
                _ = await _creativeSetSnapshotService.CaptureSnapshotWrapper(int.Parse(creativeSetId), context.CancellationToken);

                return CreativeSetResponseDto.Convert(creativeSet.Value);
            });
    }

    private void CreateSizesInCreativeSet(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<CreativesetResponseType>(GraphQlMutationName.CreateSizesInCreativeSet,
            "Creates new sizes in creativeSet",
            new QueryArguments(new List<QueryArgument>
            {
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.CreativeSetId,
                    Description = "Id of creativeSet to create sizes in"
                },
                new QueryArgument<NonNullGraphType<ListGraphType<SizeInputType>>>
                {
                    Name = ArgumentName.Sizes,
                    Description = "Sizes to create"
                }
            }),
            async context =>
            {
                var creativeSetId = context.GetArgument<string>(ArgumentName.CreativeSetId);
                var sizes = context.GetArgument<SizeDto[]>(ArgumentName.Sizes);

                if (string.IsNullOrWhiteSpace(creativeSetId))
                {
                    return context.Errors.AddError(ErrorMessages.MustBeProvided(nameof(creativeSetId)));
                }

                var sizesValidations = Assertion.Is(Assertion.NotEmpty(sizes, nameof(sizes)));
                if (!sizesValidations.IsSuccess)
                {
                    return context.Errors.AddError(sizesValidations.ErrorMessage);
                }

                var brandIdResult = await _creativeSetBrandService.GetBrandId(creativeSetId);
                if (!brandIdResult.IsSuccess)
                {
                    return context.Errors.AddNotFoundError<CreativesetEntity>();
                }

                var authorized =
                    await _authorizationService.HasAccess(brandIdResult.Value, creativeSetId, context.FieldName);

                if (!authorized)
                {
                    return context.Errors.AddForbiddenError();
                }

                var user = _userProvider.GetUser();
                var updatedCreativeSet = await _createSizesService.Create(sizes, creativeSetId, user, brandIdResult.Value);

                try
                {
                    if (!updatedCreativeSet.IsSuccess)
                    {
                        return context.Errors.AddError(updatedCreativeSet.ErrorMessage);
                    }

                    var converted = CreativeSetResponseDto.Convert(updatedCreativeSet.Value);

                    return converted;
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                    throw;
                }
            });
    }

    private void DeleteSizesInCreativeSet(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<CreativesetResponseType>(GraphQlMutationName.DeleteSizesInCreativeSet,
            "Delete a size in a creativeSet.",
            new QueryArguments(new List<QueryArgument>
            {
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.CreativeSetId,
                    Description = "Id of creativeSet whose provided sizes to delete"
                },
                new QueryArgument<NonNullGraphType<ListGraphType<StringGraphType>>>
                {
                    Name = ArgumentName.Ids,
                    Description = "Ids of sizes to delete"
                }
            }),
            async context =>
            {
                var sizeIds = context.GetArgument<string[]>(ArgumentName.Ids);
                var creativeSetId = context.GetArgument<string>(ArgumentName.CreativeSetId);

                if (!sizeIds.Any())
                {
                    return context.Errors.AddError(ErrorMessages.MustBeProvided(nameof(sizeIds)));
                }

                if (string.IsNullOrWhiteSpace(creativeSetId))
                {
                    return context.Errors.AddError(ErrorMessages.MustBeProvided(nameof(creativeSetId)));
                }

                var brandId = await _creativeSetBrandService.GetBrandId(creativeSetId);
                if (!brandId.IsSuccess)
                {
                    return context.Errors.AddNotFoundError<CreativesetEntity>();
                }

                var authorized =
                    await _authorizationService.HasAccess(brandId.Value, creativeSetId, context.FieldName);

                if (!authorized)
                {
                    return context.Errors.AddForbiddenError();
                }

                var user = _userProvider.GetUser();
                var result =
                    await _deleteSizesService.SoftDelete(sizeIds, creativeSetId, brandId.Value, user.UserId);

                return !result.IsSuccess
                    ? context.Errors.AddError(result.ErrorMessage)
                    : result.Value;
            });
    }
}