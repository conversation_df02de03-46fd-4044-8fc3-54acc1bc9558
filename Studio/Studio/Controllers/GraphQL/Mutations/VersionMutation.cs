using GraphQL;
using GraphQL.Types;
using Studio.Application.Common.Accounts;
using Studio.Application.Common.Interfaces;
using Studio.Application.Common.Interfaces.Services.CreativeSet;
using Studio.Application.Common.Interfaces.Services.Versions;
using Studio.Application.Dtos;
using Studio.Application.Dtos.Creative;
using Studio.Application.Services.VersionProperties;
using Studio.Common.Constants;
using Studio.Common.Results;
using Studio.Controllers.GraphQl.Extensions;
using Studio.Controllers.GraphQl.ViewModels.Common;
using Studio.Controllers.GraphQl.ViewModels.Creatives;
using Studio.Controllers.GraphQl.ViewModels.Versions;
using Studio.Domain;
using Studio.Domain.Entities;
using Studio.Domain.Exceptions;

namespace Studio.Controllers.GraphQl.Mutations;

public class VersionMutation : IMutation
{
    private readonly IAccountAccessService _accountAccessService;
    private readonly IUserAuthorizationService _authorizationService;
    private readonly ICreativeSetBrandService _creativeSetBrandService;
    private readonly IUserProvider _userProvider;
    private readonly ICreateVersionsService _createVersionsService;
    private readonly IUpdateVersionsService _updateVersionsService;
    private readonly ISetDefaultVersionService _setOriginalVersionService;
    private readonly IDeleteVersionsService _deleteVersionsService;
    private readonly IVersionPropertyValidator _versionPropertyValidator;

    public VersionMutation(
        IAccountAccessService accountAccessService,
        IUserProvider userProvider,
        ICreativeSetBrandService creativeSetBrandService,
        IUserAuthorizationService authorizationService,
        ICreateVersionsService createVersionsService,
        IUpdateVersionsService updateVersionsService,
        ISetDefaultVersionService setOriginalVersionService,
        IDeleteVersionsService deleteVersionsService,
        IVersionPropertyValidator versionPropertyValidator
    )
    {
        _accountAccessService = accountAccessService;
        _userProvider = userProvider;
        _creativeSetBrandService = creativeSetBrandService;
        _authorizationService = authorizationService;
        _createVersionsService = createVersionsService;
        _updateVersionsService = updateVersionsService;
        _setOriginalVersionService = setOriginalVersionService;
        _deleteVersionsService = deleteVersionsService;
        _versionPropertyValidator = versionPropertyValidator;
    }

    public void RegisterMutationsOn(StudioMutation studioMutation)
    {
        CreateVersions(studioMutation);
        UpdateVersions(studioMutation);
        DeleteVersions(studioMutation);
        SetOriginalVersion(studioMutation);
    }

    private void CreateVersions(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<VersionResponseType>(
            GraphQlMutationName.CreateVersions,
            "Creates new versions in a creativeSet",
            new QueryArguments(new List<QueryArgument>
            {
                new QueryArgument<ListGraphType<CreateVersionInputType>>
                {
                    Name = ArgumentName.Versions,
                    Description = "Versions to create"
                },
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.CreativeSetId,
                    Description = "ID of the creativeSet to create the version in"
                }
            }),
            async context =>
            {
                var creativeSetId = context.GetArgument<string>(ArgumentName.CreativeSetId);
                var versions = context.GetArgument<VersionDto[]>(ArgumentName.Versions);

                if (versions == null || !versions.Any())
                {
                    return context.Errors.AddError(ErrorMessages.MustBeProvided(nameof(versions)));
                }

                var versionProperties = versions.SelectMany(x => x.Properties).ToArray();
                _versionPropertyValidator.Validate(versionProperties);

                var brandIdResult = await _creativeSetBrandService.GetBrandId(creativeSetId);
                if (!brandIdResult.IsSuccess)
                {
                    return context.Errors.AddNotFoundError<CreativesetEntity>();
                }

                var canAccess =
                    await _authorizationService.HasAccess(brandIdResult.Value, creativeSetId, context.FieldName);

                if (!canAccess)
                {
                    return context.Errors.AddForbiddenError();
                }

                var allVersionLocalizationIdsExist =
                    await IsLocalizationIdsProvidedValidForBrand(brandIdResult.Value, versions, context.CancellationToken);

                if (!allVersionLocalizationIdsExist.IsSuccess)
                {
                    return context.Errors.AddError(allVersionLocalizationIdsExist.ErrorMessage);
                }

                var user = _userProvider.GetUser();
                var createResult = await _createVersionsService.CreateMany(creativeSetId, versions, user, brandIdResult.Value, context.CancellationToken);

                return !createResult.IsSuccess
                    ? context.Errors.AddError(createResult.ErrorMessage)
                    : createResult.Value;
            });
    }

    private void UpdateVersions(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<VersionResponseType>(
            GraphQlMutationName.UpdateVersions,
            "Updates versions in the creativeSet.",
            new QueryArguments(new List<QueryArgument>
            {
                new QueryArgument<ListGraphType<UpdateVersionInputType>>
                {
                    Name = ArgumentName.Versions,
                    Description = "Updated versions"
                },
                new QueryArgument<ListGraphType<UpdateCreativeInputType>>
                {
                    Name = ArgumentName.Creatives,
                    Description = "Creatives to  update"
                },
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.CreativeSetId,
                    Description = "Id of creativeSet containing version"
                }
            }),
            async context =>
            {
                var versions = context.GetArgument<VersionDto[]>(ArgumentName.Versions);
                var creativeSetId = context.GetArgument<string>(ArgumentName.CreativeSetId);
                var updateCreatives = context.GetArgument<UpdateCreativeDto[]>(ArgumentName.Creatives);

                var versionProperties = versions?.SelectMany(x => x.Properties).ToArray() ?? [];
                _versionPropertyValidator.Validate(versionProperties);

                var brandIdResult = await _creativeSetBrandService.GetBrandId(creativeSetId);
                if (!brandIdResult.IsSuccess)
                {
                    return context.Errors.AddNotFoundError<CreativesetEntity>();
                }

                var canAccess = await _authorizationService.HasBrandAccess(brandIdResult.Value);
                if (!canAccess)
                {
                    var versionAccess = await _authorizationService.HasVersionAccess(creativeSetId,
                        context.FieldName,
                        versions.Select(v => v.Id));
                    if (!versionAccess.HasVersionAccess)
                    {
                        return context.Errors.AddForbiddenError();
                    }
                }

                var allVersionLocalizationIdsExist =
                    await IsLocalizationIdsProvidedValidForBrand(brandIdResult.Value, versions, context.CancellationToken);
                if (!allVersionLocalizationIdsExist.IsSuccess)
                {
                    return context.Errors.AddError(allVersionLocalizationIdsExist.ErrorMessage);
                }

                var user = _userProvider.GetUser();
                var updateResult = await _updateVersionsService.Update(creativeSetId, versions, updateCreatives, brandIdResult.Value, user.UserId, context.CancellationToken);
                if (!updateResult.IsSuccess)
                {
                    return context.Errors.AddError(updateResult.ErrorMessage);
                }

                return updateResult.Value;
            });
    }

    private void DeleteVersions(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<DeleteResponseType>(
            GraphQlMutationName.DeleteVersions,
            "Deletes versions in the creativeSet",
            new QueryArguments(new List<QueryArgument>
            {
                new QueryArgument<ListGraphType<StringGraphType>>
                {
                    Name = ArgumentName.Ids,
                    Description = "A list of IDs of versions to delete"
                },
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.CreativeSetId,
                    Description = "The ID of the creativeSet containing the versions to delete"
                }
            }),
            async context =>
            {
                var versionIds = context.GetArgument<string[]>(ArgumentName.Ids);
                var creativeSetId = context.GetArgument<string>(ArgumentName.CreativeSetId);

                var brandIdResult = await _creativeSetBrandService.GetBrandId(creativeSetId);
                if (!brandIdResult.IsSuccess)
                {
                    return context.Errors.AddNotFoundError<CreativesetEntity>();
                }

                var canAccess =
                    await _authorizationService.HasAccess(brandIdResult.Value, creativeSetId, context.FieldName);

                if (!canAccess)
                {
                    return context.Errors.AddForbiddenError();
                }

                var user = _userProvider.GetUser();
                var result = await _deleteVersionsService.DeleteMany(versionIds, creativeSetId, brandIdResult.Value, user.UserId);

                return !result.IsSuccess
                    ? context.Errors.AddError(result.ErrorMessage)
                    : result.Value;
            });
    }

    private void SetOriginalVersion(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<VersionType>(
            GraphQlMutationName.SetOriginalVersion,
            "Sets the original version in the CreativeSet.",
            new QueryArguments(new List<QueryArgument>
            {
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.Id,
                    Description = "ID of the new original version"
                },
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.CreativeSetId,
                    Description = "ID of the creativeSet containing the original version"
                }
            }),
            async context =>
            {
                var creativeSetId = context.GetArgument<string>(ArgumentName.CreativeSetId);
                var versionId = context.GetArgument<string>(ArgumentName.Id);

                var brandIdResult = await _creativeSetBrandService.GetBrandId(creativeSetId);
                if (!brandIdResult.IsSuccess)
                {
                    return context.Errors.AddNotFoundError<CreativesetEntity>();
                }

                var canAccess =
                    await _authorizationService.HasAccess(brandIdResult.Value, creativeSetId, context.FieldName);
                if (!canAccess)
                {
                    return context.Errors.AddForbiddenError();
                }

                var result = await _setOriginalVersionService.SetDefault(versionId, creativeSetId, context.CancellationToken);
                if (!result.IsSuccess)
                {
                    return context.Errors.AddError(result.ErrorMessage);
                }

                return result.Value;
            });
    }

    private async Task<Result<bool>> IsLocalizationIdsProvidedValidForBrand(
        string brandId,
        IEnumerable<VersionDto> versions,
        CancellationToken cancellationToken
    )
    {
        BannerflowBrandDto brand = null;
        try
        {
            brand = await _accountAccessService.GetBrandById(brandId, cancellationToken);
        }
        catch (FailedExternalApiCallException e)
        {
            return Result<bool>.Failure(e.Message);
        }

        var brandLocalizationIds = brand.Localizations.Select(l => l.Id).ToHashSet();
        var versionLocalizationIds = versions.Select(v => v.LocalizationId).ToHashSet();

        return versionLocalizationIds.IsSubsetOf(brandLocalizationIds)
            ? Result<bool>.Success(true)
            : Result<bool>.Failure(ErrorMessages.NotFound("localization"));
    }
}