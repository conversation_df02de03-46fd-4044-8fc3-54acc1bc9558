using GraphQL;
using GraphQL.Types;
using MassTransit;
using Studio.Application.Common.Interfaces;
using Studio.Application.Common.Interfaces.Services.CreativeGeneration;
using Studio.Application.Common.Interfaces.Services.CreativeSet;
using Studio.Application.Dtos.CreativeGeneration;
using Studio.Common.Constants;
using Studio.Contracts.Events;
using Studio.Controllers.GraphQl.Extensions;
using Studio.Controllers.GraphQl.ViewModels.CreativeOptimization;
using Studio.Controllers.GraphQl.ViewModels.WeightCalculation;
using Studio.Domain;
using Studio.Domain.Entities;
using Studio.Dtos;

namespace Studio.Controllers.GraphQl.Mutations;

public class WeightCalculationMutation : IMutation
{
    private readonly ICreativeGenerationService _creativeGenerationService;
    private readonly ICreativeSetBrandService _creativeSetBrandService;
    private readonly IUserAuthorizationService _authorizationService;
    private readonly ILogger<WeightCalculationMutation> _logger;
    private readonly IBus _bus;
    private readonly IUserProvider _userProvider;

    public WeightCalculationMutation(
        ICreativeGenerationService creativeGenerationService,
        ICreativeSetBrandService creativeSetBrandService,
        IUserAuthorizationService authorizationService,
        ILogger<WeightCalculationMutation> logger,
        IBus bus,
        IUserProvider userProvider)
    {
        _creativeGenerationService = creativeGenerationService;
        _creativeSetBrandService = creativeSetBrandService;
        _authorizationService = authorizationService;
        _logger = logger;
        _bus = bus;
        _userProvider = userProvider;
    }

    public void RegisterMutationsOn(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<WeightCalculationResponseType>(GraphQlMutationName.BeginWeightCalculation,
            "Begins the creative weight calculation",
            new QueryArguments(new List<QueryArgument>
            {
                new QueryArgument<NonNullGraphType<ListGraphType<CreativesetCreativeIdsInputType>>>
                {
                    Name = ArgumentName.CreativesToWeigh,
                    Description = "The creatives to weigh"
                }
            }),
            async context =>
            {
                var creativesToWeighArgument =
                    context.GetArgument<List<CreativeSetCreativeIds>>(ArgumentName.CreativesToWeigh);

                if (!creativesToWeighArgument.Any())
                {
                    return context.Errors.AddError(ErrorMessages.MustBeProvided("creativesToWeigh"));
                }

                var creativesToWeigh = new Dictionary<int, List<int>>();

                foreach (var creative in creativesToWeighArgument)
                {
                    var creativeSetId = int.Parse(creative.CreativeSetId);
                    var creatives = creative.CreativeIds.Select(int.Parse).ToList();

                    if (creativesToWeigh.ContainsKey(creativeSetId))
                    {
                        var existingCreatives = creativesToWeigh[creativeSetId];
                        creativesToWeigh[creativeSetId] = existingCreatives.Concat(creatives).ToList();
                    }
                    else
                    {
                        creativesToWeigh.Add(creativeSetId, creatives);
                    }
                }

                var creativeSetIds = creativesToWeigh.Select(c => c.Key).ToList();
                using var _ = _logger.BeginScope(new Dictionary<string, object>
                {
                    { "creativeSetIds", creativeSetIds },
                    { "creativeIds", creativesToWeigh.SelectMany(c => c.Value) }
                });

                var brandIds = await _creativeSetBrandService.GetBrandIds(creativeSetIds);

                if (!brandIds.IsSuccess)
                {
                    return context.Errors.AddError(ErrorMessages.NotFound(nameof(CreativesetEntity)));
                }

                if (brandIds.Value == null || !brandIds.Value.Any())
                {
                    return context.Errors.AddError(ErrorMessages.NotFound(nameof(CreativesetEntity)));
                }

                if (brandIds.Value.Distinct().Count() > 1)
                {
                    return context.Errors.AddError(ErrorMessages.AllCreativeSetsMustBelongToSameBrand);
                }

                var brandId = brandIds.Value.First();
                var hasBrandAccess = await _authorizationService.HasBrandAccess(brandId);
                if (!hasBrandAccess)
                {
                    foreach (var creativeSetId in creativeSetIds)
                    {
                        if (!await _authorizationService.HasOperationAccess(creativeSetId.ToString(),
                                context.FieldName))
                        {
                            return context.Errors.AddForbiddenError();
                        }
                    }
                }

                var initWeightCalculationResult =
                    await _creativeGenerationService.InitCreativeGeneration(creativesToWeigh,
                        brandId,
                        Requester.CreativeWeightService,
                        CreativeGenerationDto.TriggerType.Generation,
                        context.CancellationToken);

                var userId = _userProvider.GetUser()?.UserId;

                foreach (var creativeSetId in creativeSetIds)
                {
                    await _bus.Publish(new NotifyCreativesetChangedEvent(creativeSetId, userId));
                }

                return initWeightCalculationResult.IsSuccess
                    ? initWeightCalculationResult.Value
                    : context.Errors.AddError(initWeightCalculationResult.ErrorMessage);
            }
        );
    }
}