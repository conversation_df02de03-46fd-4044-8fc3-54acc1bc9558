using GraphQL;
using GraphQL.Types;
using Studio.Application.Common.Interfaces;
using Studio.Application.Common.Interfaces.Services.Assets;
using Studio.Application.Dtos.Widgets;
using Studio.Common.Constants;
using Studio.Common.Results;
using Studio.Controllers.GraphQl.Extensions;
using Studio.Controllers.GraphQl.ViewModels.Widgets;
using Studio.Domain;
using Studio.Domain.Auth;
using Studio.Infrastructure.Models;

namespace Studio.Controllers.GraphQl.Mutations;

public class WidgetAssetMutation : IMutation
{
    private readonly IWidgetAssetService _widgetAssetService;
    private readonly IAuthorizationService _authorizationService;
    private readonly IUserProvider _userProvider;

    public WidgetAssetMutation(
        IUserProvider userProvider,
        IAuthorizationService authorizationService,
        IWidgetAssetService widgetAssetService
    )
    {
        _userProvider = userProvider;
        _authorizationService = authorizationService;
        _widgetAssetService = widgetAssetService;
    }

    public void RegisterMutationsOn(StudioMutation studioMutation)
    {
        CreateWidgetAsset(studioMutation);
        UpdateWidgetAsset(studioMutation);
    }

    private void CreateWidgetAsset(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<WidgetAssetType>(GraphQlMutationName.CreateWidgetAsset,
            "Creates a new widget asset.",
            new QueryArguments(new List<QueryArgument>
            {
                new QueryArgument<NonNullGraphType<WidgetAssetInputType>>
                {
                    Name = ArgumentName.Widget,
                    Description = "Widget asset to create"
                },
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.BrandId,
                    Description = "Brand id"
                }
            }),
            async context =>
            {
                var widgetDto = context.GetArgument<WidgetAssetDto>(ArgumentName.Widget);
                var brandId = context.GetArgument<string>(ArgumentName.BrandId);

                if (!await _authorizationService.HasBrandAccess(_userProvider.GetUser(), brandId))
                {
                    return context.Errors.AddForbiddenError();
                }

                var widgetAsset = await _widgetAssetService.Create(widgetDto.Thumbnail, widgetDto.AnimatedThumbnail);

                return !widgetAsset.IsSuccess
                    ? context.Errors.AddError(widgetAsset.ErrorMessage)
                    : WidgetAssetDto.Convert(widgetAsset.Value);
            });
    }

    private void UpdateWidgetAsset(StudioMutation studioMutation)
    {
        studioMutation.FieldAsync<WidgetAssetType>(GraphQlMutationName.UpdateWidgetAsset,
            "Updates a widget asset.",
            new QueryArguments(new List<QueryArgument>
            {
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.Id,
                    Description = "Widget asset id"
                },
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.BrandId,
                    Description = "Brand id"
                },
                new QueryArgument<NonNullGraphType<WidgetAssetInputType>>
                {
                    Name = ArgumentName.Widget,
                    Description = "Widget asset to update"
                },
                new QueryArgument<StringGraphType>
                {
                    Name = ArgumentName.AccountSlug,
                    Description =
                        "Accountslug of current account, mandatory when updating a bannerflowLibrary widget"
                }
            }),
            async context =>
            {
                var widgetAssetId = context.GetArgument<string>(ArgumentName.Id);
                var widgetAssetDto = context.GetArgument<WidgetAssetDto>(ArgumentName.Widget);
                var brandId = context.GetArgument<string>(ArgumentName.BrandId);
                var accountSlug = context.GetArgument<string>(ArgumentName.AccountSlug);

                var user = _userProvider.GetUser();
                if (!await _authorizationService.HasBrandAccess(user, brandId))
                {
                    return context.Errors.AddForbiddenError();
                }

                if (widgetAssetDto.BannerflowLibraryId != null)
                {
                    if (string.IsNullOrWhiteSpace(accountSlug))
                    {
                        return context.Errors.AddError(ErrorMessages.MustBeProvided(nameof(accountSlug)));
                    }

                    var hasPermissions = await HasPermissions(user, accountSlug);
                    if (!hasPermissions.IsSuccess)
                    {
                        return context.Errors.AddForbiddenError(hasPermissions.ErrorMessage);
                    }
                }

                var widgetAsset =
                    await _widgetAssetService.Update(widgetAssetId,
                        widgetAssetDto.Thumbnail,
                        widgetAssetDto.BannerflowLibraryId, widgetAssetDto.AnimatedThumbnail);

                return !widgetAsset.IsSuccess
                    ? context.Errors.AddError(widgetAsset.ErrorMessage)
                    : WidgetAssetDto.Convert(widgetAsset.Value);
            });
    }

    private async Task<Result<bool>> HasPermissions(StudioUser user, string accountSlug)
    {
        if (user.IsNotAnEmployee())
        {
            return Result<bool>.Failure(ErrorMessages.UserMustBeEmployee);
        }

        if (!await _authorizationService.HasPermission(user, Permissions.BannerflowLibrary, accountSlug))
        {
            return Result<bool>.Failure(ErrorMessages.UserDoesNotHavePermission);
        }

        return Result<bool>.Success(true);
    }
}