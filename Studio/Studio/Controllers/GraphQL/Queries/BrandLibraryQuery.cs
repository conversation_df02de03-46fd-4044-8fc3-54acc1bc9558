using GraphQL;
using GraphQL.Types;
using Studio.Application.Common.Interfaces;
using Studio.Application.Common.Interfaces.Services.BrandLibrary;
using Studio.Common.Constants;
using Studio.Controllers.GraphQl.Extensions;
using Studio.Controllers.GraphQl.ViewModels.BrandLibrary;
using Studio.Domain;

namespace Studio.Controllers.GraphQl.Queries;

public class BrandLibraryQuery : IQuery
{
    private readonly IUserAuthorizationService _authorizationService;
    private readonly IBrandLibraryService _brandLibraryService;

    public BrandLibraryQuery(
        IBrandLibraryService brandLibraryService,
        IUserAuthorizationService authorizationService
    )
    {
        _authorizationService = authorizationService;
        _brandLibraryService = brandLibraryService;
    }

    public void RegisterQueriesOn(StudioQuery studioQuery)
    {
        GetBrandLibraryByBrandId(studioQuery);
    }

    private void GetBrandLibraryByBrandId(StudioQuery studioQuery)
    {
        studioQuery.FieldAsync<BrandLibraryType>(QueryName.BrandLibrary,
            "Gets brand library by brand id",
            new QueryArguments(
                new QueryArgument<StringGraphType>
                {
                    Name = ArgumentName.BrandId,
                    Description = "Brand id of the brand library to retrieve"
                }
            ),
            async context =>
            {
                var brandId = context.GetArgument<string>(ArgumentName.BrandId);

                if (string.IsNullOrWhiteSpace(brandId))
                {
                    return context.Errors.AddError(ErrorMessages.MustBeProvided(nameof(brandId)));
                }

                if (!await _authorizationService.HasBrandAccess(brandId))
                {
                    return context.Errors.AddForbiddenError();
                }

                var brandLibrary = await _brandLibraryService.GetOrAddByBrandIdResult(brandId);

                return !brandLibrary.IsSuccess
                    ? context.Errors.AddError(brandLibrary.ErrorMessage)
                    : brandLibrary.Value;
            }
        );
    }
}