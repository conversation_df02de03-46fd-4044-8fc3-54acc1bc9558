using GraphQL;
using GraphQL.Types;
using Studio.Application.Common.Accounts;
using Studio.Application.Common.Interfaces;
using Studio.Application.Dtos;
using Studio.Controllers.GraphQl.Extensions;
using Studio.Controllers.GraphQl.ViewModels.Brand;
using Studio.Domain;
using Studio.Domain.Exceptions;

namespace Studio.Controllers.GraphQl.Queries;

public class BrandQuery : IQuery
{
    private readonly IAccountAccessService _accountAccessService;
    private readonly IUserAuthorizationService _authorizationService;

    public BrandQuery(
        IAccountAccessService accountAccessService,
        IUserAuthorizationService authorizationService
    )
    {
        _accountAccessService = accountAccessService;
        _authorizationService = authorizationService;
    }

    public void RegisterQueriesOn(StudioQuery studioQuery)
    {
        GetBrandById(studioQuery);
    }

    private void GetBrandById(StudioQuery studioQuery)
    {
        studioQuery.FieldAsync<BrandType>(QueryName.Brand,
            "Get brand by id",
            new QueryArguments
            {
                new QueryArgument<StringGraphType>
                {
                    Name = ArgumentName.Id,
                    Description = "Id of brand"
                }
            },
            async context =>
            {
                var brandId = context.GetArgument<string>(ArgumentName.Id);
                var hasBrandAccess = await _authorizationService.HasBrandAccess(brandId);

                if (!hasBrandAccess)
                {
                    return context.Errors.AddForbiddenError();
                }

                try
                {
                    var brand = await _accountAccessService.GetBrandById(brandId, context.CancellationToken);
                    return new BrandDto
                    {
                        Id = brandId,
                        Localizations = brand.Localizations,
                        SizeFormats = brand.SizeFormats,
                        Palettes = brand.Palettes,
                        Slug = brand.Slug,
                        AccountSlug = brand.AccountSlug,
                        BrandLogoUrl = brand.BrandLogoUrl,
                        Name = brand.Name,
                        ImageDefaultNameConvention = brand.StudioImageDefaultNameConvention,
                        VideoDefaultNameConvention = brand.StudioVideoDefaultNameConvention
                    };
                }
                catch (FailedExternalApiCallException ex)
                {
                    return context.Errors.AddError(ex.Message);
                }
            });
    }
}