using GraphQL;
using GraphQL.Types;
using Studio.Application.Common.Interfaces;
using Studio.Application.Common.Interfaces.Services.CreativeSet;
using Studio.Common.Constants;
using Studio.Controllers.GraphQl.Extensions;
using Studio.Controllers.GraphQl.ViewModels.CreativeSetShowcase;
using Studio.Domain;
using Studio.Domain.Entities;

namespace Studio.Controllers.GraphQl.Queries;

public class CreativeSetShowcaseQuery : IQuery
{
    private readonly ICreativeSetShowcaseService _showcaseService;
    private readonly IUserAuthorizationService _authorizationService;
    private readonly ICreativeSetBrandService _creativeSetBrandService;

    public CreativeSetShowcaseQuery(
        ICreativeSetShowcaseService showcaseService,
        IUserAuthorizationService authorizationService,
        ICreativeSetBrandService creativeSetBrandService
    )
    {
        _showcaseService = showcaseService;
        _authorizationService = authorizationService;
        _creativeSetBrandService = creativeSetBrandService;
    }

    public void RegisterQueriesOn(StudioQuery studioQuery)
    {
        GetByCreativeSetId(studioQuery);
    }

    private void GetByCreativeSetId(StudioQuery studioQuery)
    {
        studioQuery.FieldAsync<ShowcasesByCreativesetType>(
            QueryName.ShowcasesByCreativeSetId,
            "Gets all showcases for creativeSet",
            new QueryArguments(
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.CreativeSetId,
                    Description = "Id of the creativeSet"
                }
            ),
            async context =>
            {
                var creativeSetId = context.GetArgument<string>(ArgumentName.CreativeSetId);
                var brandId = await _creativeSetBrandService.GetBrandId(creativeSetId);

                if (!brandId.IsSuccess)
                {
                    return context.Errors.AddError(ErrorMessages.NotFound(nameof(CreativesetEntity)));
                }

                if (brandId.Value == null)
                {
                    return context.Errors.AddNotFoundError<CreativesetEntity>();
                }

                var canAccess = await _authorizationService.HasAccess(brandId.Value, creativeSetId, context.FieldName);

                if (!canAccess)
                {
                    return context.Errors.AddForbiddenError();
                }

                var showcaseResult = await _showcaseService.GetByCreativeSetId(creativeSetId, brandId.Value, context.CancellationToken);

                if (!showcaseResult.IsSuccess)
                {
                    return context.Errors.AddError(showcaseResult.ErrorMessage);
                }

                return showcaseResult.Value;
            });
    }
}