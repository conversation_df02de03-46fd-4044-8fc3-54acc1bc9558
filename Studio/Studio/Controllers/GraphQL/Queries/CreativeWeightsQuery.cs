using GraphQL;
using GraphQL.Types;
using Studio.Application.Common.Interfaces;
using Studio.Application.Common.Interfaces.Services.Creative;
using Studio.Application.Common.Interfaces.Services.CreativeSet;
using Studio.Application.Common.Interfaces.Services.CreativeWeightCalculation;
using Studio.Application.Dtos;
using Studio.Common.Constants;
using Studio.Controllers.GraphQl.Extensions;
using Studio.Controllers.GraphQl.ViewModels.WeightCalculation;
using Studio.Domain;
using Studio.Domain.Entities;

namespace Studio.Controllers.GraphQl.Queries;

public class CreativeWeightsQuery : IQuery
{
    private readonly ICreativeSetBrandService _creativeSetBrandService;
    private readonly IUserAuthorizationService _authorizationService;
    private readonly ICreativeWeightsService _creativeWeightsService;
    private readonly IGetCreativeService _creativeService;

    public CreativeWeightsQuery(
        ICreativeSetBrandService creativeSetBrandService,
        IUserAuthorizationService authorizationService,
        ICreativeWeightsService creativeWeightsService,
        IGetCreativeService creativeService
    )
    {
        _authorizationService = authorizationService;
        _creativeSetBrandService = creativeSetBrandService;
        _creativeWeightsService = creativeWeightsService;
        _creativeService = creativeService;
    }

    public void RegisterQueriesOn(StudioQuery studioQuery)
    {
        GetCreativeWeightsByChecksums(studioQuery);
    }

    private void GetCreativeWeightsByChecksums(StudioQuery studioQuery)
    {
        studioQuery.FieldAsync<ListGraphType<CreativeWeightsType>>(QueryName.CreativeWeightsByChecksums,
            "Get creative weights by creative checksums",
            new QueryArguments
            {
                new QueryArgument<ListGraphType<StringGraphType>>
                {
                    Name = ArgumentName.CreativeChecksumList,
                    Description = "List of creative Checksums"
                }
            },
            async context =>
            {
                var creativeChecksumList = context.GetArgument<List<string>>(ArgumentName.CreativeChecksumList);
                if (creativeChecksumList.All(string.IsNullOrWhiteSpace))
                {
                    return context.Errors.AddError(
                        ErrorMessages.MustBeProvided(nameof(ArgumentName.CreativeChecksumList)));
                }

                var creativeWeights =
                    await _creativeWeightsService.GetCreativeWeightsByChecksums(creativeChecksumList);
                if (!creativeWeights.IsSuccess)
                {
                    return context.Errors.AddNotFoundError<CreativeWeightEntity>();
                }

                var creative = await _creativeService.GetById(creativeWeights.Value.First().CreativeId);
                if (!creative.IsSuccess)
                {
                    return context.Errors.AddNotFoundError<CreativeEntity>();
                }

                var creativeSetId = creative.Value.CreativesetId;

                var brandId = await _creativeSetBrandService.GetBrandId(creativeSetId.ToString());
                if (!brandId.IsSuccess)
                {
                    return context.Errors.AddNotFoundError<ListGraphType<CreativeWeightsType>>();
                }

                if (brandId.Value == null)
                {
                    return context.Errors.AddNotFoundError<ListGraphType<CreativeWeightsType>>();
                }

                var isAllowed =
                    await _authorizationService.HasAccess(brandId.Value,
                        creativeSetId.ToString(),
                        context.FieldName);

                return !isAllowed
                    ? context.Errors.AddForbiddenError()
                    : creativeWeights.Value.Select(CreativeWeightsDto.Convert);
            });
    }
}