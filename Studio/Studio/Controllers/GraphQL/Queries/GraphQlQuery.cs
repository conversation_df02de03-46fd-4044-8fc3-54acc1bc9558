using Newtonsoft.Json;
namespace Studio.Controllers.GraphQl.Queries;

public class GraphQlQuery
{
    public string OperationName { get; set; }
    public string Query { get; set; }
    public string SerializedVariables
    {
        get
        {
            if (Variables == null)
            {
                return null;
            }

            if (!Variables.Keys.Any())
            {
                return null;
            }

            return JsonConvert.SerializeObject(Variables);
        }
    }
    public Dictionary<string, object> Variables { get; set; }
}