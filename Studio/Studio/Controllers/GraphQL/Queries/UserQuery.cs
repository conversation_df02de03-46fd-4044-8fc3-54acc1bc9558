using GraphQL;
using GraphQL.Types;
using Studio.Application.Common.Accounts;
using Studio.Application.Common.Interfaces;
using Studio.Controllers.GraphQl.Extensions;
using Studio.Controllers.GraphQl.ViewModels.User;
using Studio.Domain;
using Studio.Dtos;

namespace Studio.Controllers.GraphQl.Queries;

public class UserQuery : IQuery
{
    private readonly IUserProvider _userProvider;
    private readonly IAccountAccessService _accountService;

    public UserQuery(
        IUserProvider userProvider,
        IAccountAccessService accountService
    )
    {
        _userProvider = userProvider;
        _accountService = accountService;
    }

    public void RegisterQueriesOn(StudioQuery studioQuery)
    {
        GetUser(studioQuery);
    }

    public void GetUser(StudioQuery studioQuery)
    {
        studioQuery.FieldAsync<UserType>(QueryName.User,
            "Get user information",
            new QueryArguments(
                new QueryArgument<StringGraphType>
                {
                    Name = ArgumentName.Id,
                    Description = "Id of the user"
                },
                new QueryArgument<NonNullGraphType<StringGraphType>>
                {
                    Name = ArgumentName.AccountSlug,
                    Description = "Accountslug of current account"
                }
            ),
            async context =>
            {
                var userId = context.GetArgument<string>(ArgumentName.Id);
                var accountSlug = context.GetArgument<string>(ArgumentName.AccountSlug);
                var user = _userProvider.GetUser();

                if (!string.IsNullOrWhiteSpace(userId) && !user.UserId.Equals(userId))
                {
                    return context.Errors.AddForbiddenError("The user id provided does not match the current logged in user id.");
                }

                // if user is employee we need to check permissions for current account and not from the token
                Account account;
                if (user.IsAnEmployee())
                {
                    account = await _accountService.GetAccountBySlug(accountSlug, user.UserId);
                }
                else
                {
                    account = await _accountService.GetAccountById(user.AmAccountId, user.UserId);
                }

                return new UserDto
                {
                    IsEmployee = user.IsAnEmployee(),
                    Permissions = account.AccountPermissions != null
                        ? account.AccountPermissions.ToList()
                        : new List<string>(),
                    Role = (int)user.Role,
                };
            });
    }
}