using GraphQL;
using GraphQL.Language.AST;
using GraphQL.Validation;
using Newtonsoft.Json;
using Studio.Controllers.GraphQl.ViewModels.Elements;
using Studio.Controllers.GraphQl.ViewModels.Versions;

namespace Studio.Controllers.GraphQl.Validation;

public class CheckForBase64InputValidationRule : IValidationRule
{
    private static readonly string[] TypesToValidate =
    {
        nameof(CreateElementInputType),
        nameof(ElementInputType),
        nameof(CreateVersionInputType),
        nameof(UpdateVersionInputType)
    };

    public async Task<INodeVisitor> ValidateAsync(ValidationContext context)
    {
        return await Task.FromResult(Validate(context));
    }

    private static INodeVisitor Validate(ValidationContext context)
    {
        return new EnterLeaveListener(_ =>
        {
            _.Match<Argument>(inputArgument =>
            {
                var argDef = context.TypeInfo.GetArgument();

                if (argDef == null)
                {
                    return;
                }

                if (context.Inputs == null || !context.Inputs.Any())
                {
                    return;
                }

                var namedType = argDef.ResolvedType.GetNamedType();

                if (!MatchesType(namedType.Name))
                {
                    return;
                }

                var payload = JsonConvert.SerializeObject(context.Inputs);

                if (!payload.Contains("base64,", StringComparison.InvariantCultureIgnoreCase))
                {
                    return;
                }

                context.ReportError(new ValidationError(
                    context.OriginalQuery,
                    "400",
                    "Base64 Content detected",
                    inputArgument));
            });
        });
    }

    private static bool MatchesType(string typeName)
    {
        return TypesToValidate.Any(x => x.Equals(typeName, StringComparison.InvariantCultureIgnoreCase));
    }
}