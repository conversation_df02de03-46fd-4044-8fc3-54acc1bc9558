using GraphQL;
using GraphQL.Language.AST;
using GraphQL.Types;
using GraphQL.Validation;
using Newtonsoft.Json;
using Studio.Common.Interfaces.Configuration;
using Studio.Controllers.GraphQl.ViewModels.Elements;
using Studio.Controllers.GraphQl.ViewModels.Versions;
using System.Collections.Concurrent;

namespace Studio.Controllers.GraphQl.Validation;

public class CheckLengthOfTextElementValidationRule : IValidationRule
{
    private static readonly Func<Dictionary<string, object>> EmptyDictionary = () =>
        new Dictionary<string, object>(0);

    private static readonly ConcurrentDictionary<string, Func<Dictionary<string, object>, int, bool>>
        InputValidators =
            new ConcurrentDictionary<string, Func<Dictionary<string, object>, int, bool>>
            {
                [nameof(CreateElementInputType)] = ValidateElementInputType,
                [nameof(ElementInputType)] = ValidateElementInputType,
                [nameof(CreateVersionInputType)] = ValidateVersionInputType,
                [nameof(UpdateVersionInputType)] = ValidateVersionInputType
            };

    private readonly IAppSettings _settings;

    public CheckLengthOfTextElementValidationRule(IAppSettings settings)
    {
        _settings = settings;
    }

    public async Task<INodeVisitor> ValidateAsync(ValidationContext context)
    {
        return await Task.FromResult(Validate(context));
    }

    private INodeVisitor Validate(ValidationContext context)
    {
        return new EnterLeaveListener(_ =>
        {
            _.Match<Argument>(inputArgument =>
            {
                QueryArgument argDef = context.TypeInfo.GetArgument();

                if (argDef == null)
                {
                    return;
                }

                if (context.Inputs == null || !context.Inputs.Any())
                {
                    return;
                }

                if (!context.Inputs.ContainsKey(argDef.Name))
                {
                    return;
                }

                var namedType = argDef.ResolvedType.GetNamedType();

                if (!InputValidators.ContainsKey(namedType.Name))
                {
                    return;
                }

                var input = context.Inputs[argDef.Name];
                var inputDictionaries = ExtractDictionaries(input);

                var validationResults = inputDictionaries.Select(x =>
                    InputValidators[namedType.Name](x, _settings.MaximumTextLength));

                if (validationResults.All(x => x))
                {
                    return;
                }

                context.ReportError(new ValidationError(
                    context.OriginalQuery,
                    "400",
                    $"Text cannot have length longer then {_settings.MaximumTextLength} characters",
                    inputArgument));
            });
        });
    }

    private static IEnumerable<Dictionary<string, object>> ExtractDictionaries(object value)
    {
        if (value == null)
        {
            yield return EmptyDictionary();
            yield break;
        }

        if (value is Dictionary<string, object> convertedDictionary)
        {
            yield return convertedDictionary;
            yield break;
        }

        if (value is not List<object>)
        {
            yield return EmptyDictionary();
            yield break;
        }

        var list = (List<object>)value;

        if (!list.Any())
        {
            yield return EmptyDictionary();
            yield break;
        }

        foreach (Dictionary<string, object> dictionary in list)
        {
            yield return dictionary;
        }
    }

    // First we need to get dictionaries that represent all element's properties which are stored under key: `properties`
    // The text content we want to validate is inside KV pair with key: `name` and the value: `inline-styled-text`
    private static bool ValidateElementInputType(Dictionary<string, object> inputDictionary, int maximumTextLength)
    {
        var elementProperties = ExtractDictionaries(GetDictionaryValue("properties", inputDictionary));

        foreach (var elementProperty in elementProperties)
        {
            var nameValue = GetDictionaryValue("name", elementProperty);

            if (nameValue == null)
            {
                continue;
            }

            if (!nameValue.ToString().Equals("inline-styled-text", StringComparison.InvariantCultureIgnoreCase))
            {
                continue;
            }

            return CheckPropertyValue(elementProperty, maximumTextLength);
        }

        return true;
    }

    // The text values that we need to validate are under key: `versions` which stores VersionProperty data
    private static bool ValidateVersionInputType(Dictionary<string, object> inputDictionary, int maximumTextLength)
    {
        var versionDictionaries = ExtractDictionaries(GetDictionaryValue("properties", inputDictionary));

        var isValid = true;

        foreach (var version in versionDictionaries)
        {
            //Skips version properties that are not flagged as content
            var name = GetDictionaryValue("name", version);
            if (name != null && !name.ToString()!.Equals("content", StringComparison.OrdinalIgnoreCase))
            {
                continue;
            }

            isValid &= CheckPropertyValue(version, maximumTextLength);
        }

        return isValid;
    }

    private static object GetDictionaryValue(string key, IReadOnlyDictionary<string, object> dictionary)
    {
        var value = dictionary.Keys.FirstOrDefault(x => x.Equals(key, StringComparison.InvariantCultureIgnoreCase));

        return string.IsNullOrWhiteSpace(value) ? null : dictionary[value];
    }

    // VersionProperty/ElementProperty dictionary always contains key: `value`
    // Where `value` holds the json payload that has property: `text` that contains actual text that we need to validate.
    private static bool CheckPropertyValue(IReadOnlyDictionary<string, object> properties, int maximumTextLength)
    {
        var value = GetDictionaryValue("value", properties);

        if (value == null)
        {
            return true;
        }

        var text = ExtractTextValueFromJson(value.ToString());

        return text.Length <= maximumTextLength;
    }

    private static string ExtractTextValueFromJson(string json)
    {
        var valueTemplate = new { text = string.Empty };
        var propertyText = JsonConvert.DeserializeAnonymousType(json, valueTemplate);

        return propertyText.text;
    }
}