using GraphQL.Language.AST;
using GraphQL.Validation;

namespace Studio.Controllers.GraphQl.Validation;

/// <summary>
/// Analyzes the document for any introspection fields and reports an error if any are found.
/// </summary>
public class NoIntrospectionValidationRule : IValidationRule
{
    /// <inheritdoc/>
    public async Task<INodeVisitor> ValidateAsync(ValidationContext context)
    {
        return await Task.FromResult(Validate(context));
    }

    private INodeVisitor Validate(ValidationContext context)
    {
        return new EnterLeaveListener(_ =>
        {
            _.Match<Field>(field =>
            {
                if (field.Name is "__schema" or "__type")
                {
                    context.ReportError(new ValidationError(
                        context.OriginalQuery,
                        "400",
                        "Introspection queries are not allowed.",
                        field));
                }
            });
        });
    }
}