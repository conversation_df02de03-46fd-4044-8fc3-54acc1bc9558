using GraphQL.Types;

namespace Studio.Controllers.GraphQl.ViewModels.Assets;

public class ImageAssetInputType : InputObjectGraphType
{
    public ImageAssetInputType()
    {
        Name = "ImageAssetInput";
        Field<NonNullGraphType<StringGraphType>>("name", "The filename of the image asset");
        Field<ImageInputType>("original", "The unscaled image");
        Field<ImageInputType>("thumbnail", "The reduced-size version of the original image");
        Field<ImageInputType>("animatedThumbnail", "The reduced-size version of the original image, animated version");
        Field<NonNullGraphType<IntGraphType>>("fileSize", "The file size of the original image");
    }
}