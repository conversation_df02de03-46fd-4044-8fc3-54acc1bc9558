using GraphQL.Types;
using Studio.Application.Dtos;
using Studio.Domain.Enums;

namespace Studio.Controllers.GraphQl.ViewModels.Assets;

public class ImageAssetType : ObjectGraphType<ImageAssetDto>
{
    public ImageAssetType()
    {
        Field(x => x.Id).Description("The id of image asset");
        Field(x => x.Name, nullable: true).Description("The filename of the image asset");
        Field<EnumerationGraphType<State>>(
            "_state",
            resolve: context => context.Source.State,
            description: "The state of the image asset");
        Field<ImageType>("original",
            resolve: context => context.Source.Original,
            description: "The unscaled image");
        Field<ImageType>("thumbnail",
            resolve: context => context.Source.Thumbnail,
            description: "The reduced-size version of the original image");
        Field<ImageType>("animatedThumbnail",
            resolve: context => context.Source.AnimatedThumbnail,
            description: "The reduced-size animated (gif) version of the original image");
        Field(x => x.FileSize, nullable: true).Description("The file size of the original image");
        Field<DateTimeGraphType>("modified",
                resolve: context => context.Source.Modified,
                description: "When the image asset was last modified, if at all");
        Field<DateTimeGraphType>("created",
            resolve: context => context.Source.Created,
            description: "Image asset creation date");
        Field(x => x.IsGenAi, nullable: true).Description("Determines whether the image is generated by AI");
    }
}