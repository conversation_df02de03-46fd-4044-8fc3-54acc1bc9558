using GraphQL.Types;

namespace Studio.Controllers.GraphQl.ViewModels.Assets;

public class ImageInputType : InputObjectGraphType
{
    public ImageInputType()
    {
        Name = "ImageInput";

        Field<NonNullGraphType<IntGraphType>>("width", "The width of the image asset");
        Field<NonNullGraphType<IntGraphType>>("height", "The height of the image asset");
        Field<NonNullGraphType<StringGraphType>>("url", "The public url to the image asset");
    }
}