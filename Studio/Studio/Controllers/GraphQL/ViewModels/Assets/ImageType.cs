using GraphQL.Types;
using Studio.Application.Dtos;

namespace Studio.Controllers.GraphQl.ViewModels.Assets;

public class ImageType : ObjectGraphType<ImageDto>
{
    public ImageType()
    {
        Field(x => x.Url).Description("The public url of the image");
        Field(x => x.Width).Description("The width of the image");
        Field(x => x.Height).Description("The height of the image");
    }
}