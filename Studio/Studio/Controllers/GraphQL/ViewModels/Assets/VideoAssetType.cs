using GraphQL.Types;
using Studio.Application.Dtos;
using Studio.Domain.Enums;

namespace Studio.Controllers.GraphQl.ViewModels.Assets;

public class VideoAssetType : ObjectGraphType<VideoAssetDto>
{
    public VideoAssetType()
    {
        Field(x => x.Id).Description("The id of video asset");
        Field(x => x.Name, nullable: false).Description("The filename of the video asset");
        Field(x => x.Url, nullable: false).Description("The url of the video asset");
        Field(x => x.FileSize, nullable: false).Description("The file size of the video");
        Field(x => x.Width, nullable: false).Description("The width of the video");
        Field(x => x.Height, nullable: false).Description("The height of the video");
        Field(x => x.DurationInMilliseconds, nullable: false).Description("The duration of the video in milliseconds");
        Field<ImageType>("thumbnail",
            resolve: context => context.Source.Thumbnail,
            description: "The thumbnail of the video");
        Field<EnumerationGraphType<State>>(
            "_state",
            resolve: context => context.Source.State,
            description: "The state of the video asset");
        Field<DateTimeGraphType>("modified",
                resolve: context => context.Source.Modified,
                description: "When the image asset was last modified, if at all");
        Field<DateTimeGraphType>("created",
            resolve: context => context.Source.Created,
            description: "Video asset creation date");
    }
}