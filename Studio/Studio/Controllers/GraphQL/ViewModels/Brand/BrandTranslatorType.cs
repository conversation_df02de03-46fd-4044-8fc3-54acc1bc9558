using GraphQL.Types;
using Studio.Application.Dtos;
using Studio.Controllers.GraphQl.ViewModels.Localizations;

namespace Studio.Controllers.GraphQl.ViewModels.Brand;

public class BrandTranslatorType : ObjectGraphType<BrandTranslatorDto>
{
    public BrandTranslatorType()
    {
        Field(t => t.Id).Description("Translator id");
        Field(t => t.Name).Description("Translator name");
        Field(t => t.Email).Description("Translator email");
        Field<ListGraphType<LocalizationType>>("localizations",
            resolve: ctx => ctx.Source.Localizations,
            description: "Translator's localizations");
    }
}