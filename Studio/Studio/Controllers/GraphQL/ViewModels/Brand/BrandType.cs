using GraphQL.Types;
using Studio.Application.Dtos;
using Studio.Controllers.GraphQl.ViewModels.Localizations;
using Studio.Controllers.GraphQl.ViewModels.Palettes;
using Studio.Controllers.GraphQl.ViewModels.SizeFormat;

namespace Studio.Controllers.GraphQl.ViewModels.Brand;

public class BrandType : ObjectGraphType<BrandDto>
{
    public BrandType()
    {
        Field(x => x.Id).Description("Id of brand");
        Field(x => x.AccountSlug).Description("The account slug of this brand");
        Field(x => x.Slug).Description("The slug of this brand");
        Field(x => x.Name, nullable: true).Description("The name of this brand");
        Field(x => x.BrandLogoUrl, nullable: true).Description("The url to the brand logo");
        Field(x => x.ImageDefaultNameConvention, nullable: true).Description("Default name conventions for studio images");
        Field(x => x.VideoDefaultNameConvention, nullable: true).Description("Default name conventions for studio videos");
        Field<ListGraphType<LocalizationType>>(
            "localizations",
            resolve: context => context.Source.Localizations,
            description: "The localizations for this brand");
        Field<ListGraphType<SizeFormatType>>(
            "sizeFormats",
            resolve: context => context.Source.SizeFormats,
            description: "The size formats for this brand ");
        Field<ListGraphType<PaletteType>>(
            "palettes",
            resolve: context => context.Source.Palettes,
            description: "The palettes for this brand "
        );
    }
}