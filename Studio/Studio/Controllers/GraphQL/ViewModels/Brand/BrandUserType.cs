using GraphQL.Types;
using Studio.Application.Dtos;

namespace Studio.Controllers.GraphQl.ViewModels.Brand;

public class BrandUserType : ObjectGraphType<BrandUserDto>
{
    public BrandUserType()
    {
        Field(u => u.Id).Description("User id");
        Field(u => u.FirstName, true).Description("User first name");
        Field(u => u.LastName, true).Description("User last name");
        Field(u => u.Email, true).Description("User email");
    }
}