using GraphQL.Types;
using Studio.Application.Dtos.BrandLibrary;

namespace Studio.Controllers.GraphQl.ViewModels.BrandLibrary;

public class BrandLibraryFolderType : ObjectGraphType<BrandLibraryFolderDto>
{
    public BrandLibraryFolderType()
    {
        Field(x => x.Id).Description("The id of the folder");
        Field(x => x.Name).Description("The name of the element");
        Field(x => x.ParentFolderId, nullable: true).Description("Parent folder");
    }
}