using GraphQL.Types;
using Studio.Application.Dtos.BrandLibrary;
using Studio.Controllers.GraphQl.ViewModels.Assets;
using Studio.Controllers.GraphQl.ViewModels.Widgets;

namespace Studio.Controllers.GraphQl.ViewModels.BrandLibrary;

// TODO: Decide access modifier for all types
internal class BrandLibraryType : ObjectGraphType<BrandLibraryDto>
{
    public BrandLibraryType()
    {
        Field(x => x.Id).Description("The id of the brand library");
        Field(x => x.BrandId).Description("The brand id of the brand library");
        Field<ListGraphType<Elements.ElementType>>("elements",
            resolve: context => context.Source.Elements,
            description: "The brand library elements");
        Field<ListGraphType<ImageAssetType>>("images",
            resolve: context => context.Source.Images,
            description: "The brand library images");
        Field<ListGraphType<VideoAssetType>>("videos",
            resolve: context => context.Source.Videos,
            description: "The brand library videos");
        Field<ListGraphType<WidgetAssetType>>("widgets",
            resolve: context => context.Source.Widgets,
            description: "The brand library widgets");
        Field<ListGraphType<BrandLibraryFolderType>>("folders",
            resolve: context => context.Source.Folders);
    }
}