using GraphQL.Types;

namespace Studio.Controllers.GraphQl.ViewModels.CharacterSpanStyle;

public class CharacterSpanStyleType : ObjectGraphType
{
    public CharacterSpanStyleType()
    {
        Field<NonNullGraphType<IntGraphType>>("position", "The position of the character span style");
        Field<NonNullGraphType<IntGraphType>>("length", "Length of the character span style");
        Field<NonNullGraphType<StringGraphType>>("style", "Semicolon-separated list of styles");
    }
}